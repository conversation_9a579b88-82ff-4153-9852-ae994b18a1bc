@startuml 油井设备监控系统架构图
!theme aws-orange
!define AWSPUML https://raw.githubusercontent.com/awslabs/aws-icons-for-plantuml/v15.0/dist
!includeurl AWSPUML/AWSCommon.puml
!includeurl AWSPUML/ApplicationIntegration/APIGateway.puml
!includeurl AWSPUML/Compute/EC2.puml
!includeurl AWSPUML/Database/RDS.puml
!includeurl AWSPUML/Database/ElastiCache.puml
!includeurl AWSPUML/Analytics/Elasticsearch.puml
!includeurl AWSPUML/NetworkingContentDelivery/CloudFront.puml

title 油井设备监控系统架构图\n基于 PHP 8.3 + Swoole + Vue 3 + EMQX

skinparam backgroundColor #FAFAFA
skinparam defaultFontName "Microsoft YaHei"

' 定义颜色
skinparam rectangle {
    BackgroundColor<<device>> #E1F5FE
    BorderColor<<device>> #01579B
    BackgroundColor<<comm>> #F3E5F5
    BorderColor<<comm>> #4A148C
    BackgroundColor<<backend>> #E8F5E8
    BorderColor<<backend>> #1B5E20
    BackgroundColor<<data>> #FFF3E0
    BorderColor<<data>> #E65100
    BackgroundColor<<frontend>> #E3F2FD
    BorderColor<<frontend>> #0D47A1
    BackgroundColor<<monitor>> #FCE4EC
    BorderColor<<monitor>> #880E4F
    BackgroundColor<<external>> #F1F8E9
    BorderColor<<external>> #33691E
}

' 设备层
package "设备层 (Device Layer)" <<device>> {
    rectangle "油井设备 1\n传感器/执行器" as device1 <<device>>
    rectangle "油井设备 2\n传感器/执行器" as device2 <<device>>
    rectangle "油井设备 N\n传感器/执行器" as deviceN <<device>>
    
    package "设备数据" {
        rectangle "内压/外压" as pressure
        rectangle "流量数据" as flow
        rectangle "温度数据" as temperature
        rectangle "阀门开度" as valve
        rectangle "设备状态" as status
    }
}

' 通信层
package "通信层 (Communication Layer)" <<comm>> {
    rectangle "EMQX MQTT Broker\n消息代理服务器" as emqx <<comm>>
    rectangle "MQTT Protocol\nWebSocket\nHTTP/HTTPS" as protocols <<comm>>
}

' 后端服务层
package "后端服务层 (Backend Services)" <<backend>> {
    rectangle "Nginx\n负载均衡器" as nginx <<backend>>
    
    package "PHP 8.3 + Swoole 集群" {
        rectangle "API服务器 1\nHTTP处理" as api1 <<backend>>
        rectangle "API服务器 2\nMQTT处理" as api2 <<backend>>
        rectangle "WebSocket服务器\n实时推送" as ws <<backend>>
    }
    
    package "Swoole进程管理" {
        rectangle "Master进程\n进程管理" as master <<backend>>
        rectangle "Worker进程\nHTTP/MQTT处理" as worker <<backend>>
        rectangle "Task进程\n异步任务" as task <<backend>>
    }
}

' 数据存储层
package "数据存储层 (Data Storage)" <<data>> {
    database "MySQL 8 主库\n设备信息/用户数据" as mysql_master <<data>>
    database "MySQL 8 从库1\n读取负载分担" as mysql_slave1 <<data>>
    database "MySQL 8 从库2\n读取负载分担" as mysql_slave2 <<data>>
    
    database "Redis 主节点\n实时数据缓存" as redis_master <<data>>
    database "Redis 从节点1\n会话存储" as redis_slave1 <<data>>
    database "Redis 从节点2\n队列缓存" as redis_slave2 <<data>>
    
    database "Elasticsearch 节点1\n历史数据索引" as es1 <<data>>
    database "Elasticsearch 节点2\n日志数据存储" as es2 <<data>>
    database "Elasticsearch 节点3\n全文搜索" as es3 <<data>>
    
    storage "文件存储\nNFS/对象存储" as filestorage <<data>>
}

' 前端应用层
package "前端应用层 (Frontend Layer)" <<frontend>> {
    rectangle "Vue 3 + Vite\nhelioCloud-devops-frontend" as vue_app <<frontend>>
    
    package "状态管理" {
        rectangle "Pinia Store\n全局状态管理" as pinia <<frontend>>
    }
    
    package "样式系统" {
        rectangle "Sass/SCSS\n样式预处理" as sass <<frontend>>
    }
    
    package "功能模块" {
        rectangle "设备监控面板\nECharts图表" as dashboard <<frontend>>
        rectangle "历史数据分析\n数据查询" as history <<frontend>>
        rectangle "实时图表展示\nWebSocket连接" as realtime <<frontend>>
        rectangle "告警管理\n规则配置" as alert <<frontend>>
        rectangle "设备管理\nCRUD操作" as device_mgmt <<frontend>>
        rectangle "用户权限管理\n角色控制" as user_mgmt <<frontend>>
    }
    
    rectangle "移动端应用\nH5/小程序" as mobile <<frontend>>
}

' 监控运维层
package "监控运维层 (DevOps Layer)" <<monitor>> {
    rectangle "Zabbix\n系统监控" as zabbix <<monitor>>
    rectangle "Grafana\n监控面板" as grafana <<monitor>>
    rectangle "ELK Stack\n日志管理" as elk <<monitor>>
    rectangle "Docker\n容器化部署" as docker <<monitor>>
}

' 外部服务层
package "外部服务层 (External Services)" <<external>> {
    rectangle "短信服务\n告警通知" as sms <<external>>
    rectangle "邮件服务\nSMTP推送" as email <<external>>
    rectangle "地图服务\n设备定位" as map <<external>>
    rectangle "云服务\n阿里云/腾讯云" as cloud <<external>>
}

' 用户
actor "用户" as user

' 连接关系
' 设备数据流
pressure --> device1
flow --> device1
temperature --> device1
valve --> device1
status --> device1

device1 -.-> emqx : MQTT发布
device2 -.-> emqx : MQTT发布
deviceN -.-> emqx : MQTT发布

' 通信层到后端
emqx --> nginx : 消息转发
protocols --> emqx

' 后端服务内部
nginx --> api1 : 负载均衡
nginx --> api2 : 负载均衡
nginx --> ws : 负载均衡

master --> worker : 进程管理
master --> task : 进程管理
worker --> api1
worker --> api2
worker --> ws

' 后端到数据库
api1 --> mysql_master : 写入数据
api2 --> mysql_slave1 : 读取数据
ws --> redis_master : 缓存操作
task --> es1 : 索引数据

' 数据库主从复制
mysql_master --> mysql_slave1 : 主从复制
mysql_master --> mysql_slave2 : 主从复制
redis_master --> redis_slave1 : 主从复制
redis_master --> redis_slave2 : 主从复制

' 前端到后端
user --> vue_app : 访问
vue_app --> nginx : HTTP/HTTPS
vue_app -.-> ws : WebSocket

' 前端内部组件
pinia --> vue_app
sass --> vue_app
dashboard --> vue_app
history --> vue_app
realtime --> vue_app
alert --> vue_app
device_mgmt --> vue_app
user_mgmt --> vue_app

mobile --> nginx : HTTP/HTTPS

' 监控连接
zabbix -.-> nginx : 监控
grafana -.-> zabbix : 数据源
elk -.-> api1 : 日志收集
docker -.-> api1 : 容器管理

' 外部服务
task --> sms : 发送短信
task --> email : 发送邮件
vue_app --> map : 地图服务
nginx --> cloud : 云服务

' 数据流说明
note top of emqx : MQTT消息代理\n支持集群部署\n规则引擎处理
note top of nginx : 反向代理\n负载均衡\nSSL终端
note top of mysql_master : 关系数据库\n事务支持\n主从复制
note top of redis_master : 内存数据库\n高性能缓存\n发布订阅
note top of es1 : 搜索引擎\n全文检索\n日志分析
note top of vue_app : 现代前端框架\n组件化开发\n响应式设计

@enduml
