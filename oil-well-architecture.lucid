{"version": "1.0", "metadata": {"title": "油井设备监控系统架构图", "description": "基于PHP 8.3 + Swoole + Vue 3 + EMQX的油井设备监控系统", "author": "System Architect", "created": "2024-01-01", "technology_stack": {"frontend": ["Vue 3", "Vite", "Pinia", "Element Plus", "<PERSON><PERSON><PERSON>", "Sass"], "backend": ["PHP 8.3", "Swoole", "<PERSON><PERSON><PERSON>", "Composer"], "middleware": ["EMQX MQTT Broker"], "database": ["MySQL 8", "Redis 7", "Elasticsearch 8"], "devops": ["<PERSON>er", "Zabbix", "<PERSON><PERSON>", "ELK Stack"]}}, "canvas": {"width": 1600, "height": 1200, "background": "#FAFAFA"}, "layers": [{"id": "device-layer", "name": "设备层", "color": "#E1F5FE", "border": "#01579B", "position": {"x": 50, "y": 50}, "size": {"width": 300, "height": 150}, "elements": [{"id": "oil-well-1", "type": "rectangle", "text": "油井设备 1\n传感器/执行器", "position": {"x": 70, "y": 80}, "size": {"width": 80, "height": 60}, "style": {"fill": "#BBDEFB", "stroke": "#1976D2", "strokeWidth": 2}}, {"id": "oil-well-2", "type": "rectangle", "text": "油井设备 2\n传感器/执行器", "position": {"x": 170, "y": 80}, "size": {"width": 80, "height": 60}, "style": {"fill": "#BBDEFB", "stroke": "#1976D2", "strokeWidth": 2}}, {"id": "oil-well-n", "type": "rectangle", "text": "油井设备 N\n传感器/执行器", "position": {"x": 270, "y": 80}, "size": {"width": 80, "height": 60}, "style": {"fill": "#BBDEFB", "stroke": "#1976D2", "strokeWidth": 2}}]}, {"id": "communication-layer", "name": "通信层", "color": "#F3E5F5", "border": "#4A148C", "position": {"x": 400, "y": 50}, "size": {"width": 300, "height": 150}, "elements": [{"id": "emqx-broker", "type": "rectangle", "text": "EMQX MQTT Broker\n消息代理服务器", "position": {"x": 480, "y": 80}, "size": {"width": 140, "height": 60}, "style": {"fill": "#CE93D8", "stroke": "#7B1FA2", "strokeWidth": 2}}, {"id": "protocols", "type": "rectangle", "text": "MQTT/WebSocket\nHTTP/HTTPS", "position": {"x": 480, "y": 150}, "size": {"width": 140, "height": 30}, "style": {"fill": "#E1BEE7", "stroke": "#8E24AA", "strokeWidth": 1}}]}, {"id": "backend-layer", "name": "后端服务层", "color": "#E8F5E8", "border": "#1B5E20", "position": {"x": 750, "y": 50}, "size": {"width": 400, "height": 300}, "elements": [{"id": "nginx-lb", "type": "rectangle", "text": "Nginx\n负载均衡", "position": {"x": 910, "y": 80}, "size": {"width": 80, "height": 50}, "style": {"fill": "#A5D6A7", "stroke": "#388E3C", "strokeWidth": 2}}, {"id": "php-swoole-1", "type": "rectangle", "text": "PHP 8.3 + Swoole\nAPI服务器 1", "position": {"x": 790, "y": 150}, "size": {"width": 100, "height": 60}, "style": {"fill": "#C8E6C9", "stroke": "#4CAF50", "strokeWidth": 2}}, {"id": "php-swoole-2", "type": "rectangle", "text": "PHP 8.3 + Swoole\nAPI服务器 2", "position": {"x": 900, "y": 150}, "size": {"width": 100, "height": 60}, "style": {"fill": "#C8E6C9", "stroke": "#4CAF50", "strokeWidth": 2}}, {"id": "websocket-server", "type": "rectangle", "text": "PHP 8.3 + Swoole\nWebSocket服务", "position": {"x": 1010, "y": 150}, "size": {"width": 100, "height": 60}, "style": {"fill": "#C8E6C9", "stroke": "#4CAF50", "strokeWidth": 2}}, {"id": "swoole-processes", "type": "rectangle", "text": "Swoole进程管理\nMaster/Worker/Task", "position": {"x": 870, "y": 230}, "size": {"width": 160, "height": 50}, "style": {"fill": "#DCEDC8", "stroke": "#689F38", "strokeWidth": 2}}]}, {"id": "data-layer", "name": "数据存储层", "color": "#FFF3E0", "border": "#E65100", "position": {"x": 50, "y": 400}, "size": {"width": 700, "height": 200}, "elements": [{"id": "mysql-cluster", "type": "cylinder", "text": "MySQL 8 集群\n主从复制", "position": {"x": 90, "y": 440}, "size": {"width": 120, "height": 80}, "style": {"fill": "#FFCC80", "stroke": "#F57C00", "strokeWidth": 2}}, {"id": "redis-cluster", "type": "cylinder", "text": "Redis 集群\n缓存/会话", "position": {"x": 230, "y": 440}, "size": {"width": 120, "height": 80}, "style": {"fill": "#FFCC80", "stroke": "#F57C00", "strokeWidth": 2}}, {"id": "elasticsearch", "type": "cylinder", "text": "Elasticsearch\n搜索/日志", "position": {"x": 370, "y": 440}, "size": {"width": 120, "height": 80}, "style": {"fill": "#FFCC80", "stroke": "#F57C00", "strokeWidth": 2}}, {"id": "file-storage", "type": "rectangle", "text": "文件存储\nNFS/对象存储", "position": {"x": 510, "y": 440}, "size": {"width": 120, "height": 80}, "style": {"fill": "#FFCC80", "stroke": "#F57C00", "strokeWidth": 2}}]}, {"id": "frontend-layer", "name": "前端应用层", "color": "#E3F2FD", "border": "#0D47A1", "position": {"x": 800, "y": 400}, "size": {"width": 350, "height": 200}, "elements": [{"id": "vue3-app", "type": "rectangle", "text": "Vue 3 + Vite\n前端应用", "position": {"x": 840, "y": 440}, "size": {"width": 120, "height": 60}, "style": {"fill": "#90CAF9", "stroke": "#1976D2", "strokeWidth": 2}}, {"id": "pinia-store", "type": "rectangle", "text": "Pinia\n状态管理", "position": {"x": 980, "y": 440}, "size": {"width": 80, "height": 40}, "style": {"fill": "#BBDEFB", "stroke": "#2196F3", "strokeWidth": 1}}, {"id": "echarts", "type": "rectangle", "text": "ECharts\n图表组件", "position": {"x": 1080, "y": 440}, "size": {"width": 80, "height": 40}, "style": {"fill": "#BBDEFB", "stroke": "#2196F3", "strokeWidth": 1}}, {"id": "sass-styles", "type": "rectangle", "text": "Sass/SCSS\n样式系统", "position": {"x": 980, "y": 490}, "size": {"width": 80, "height": 40}, "style": {"fill": "#BBDEFB", "stroke": "#2196F3", "strokeWidth": 1}}, {"id": "element-plus", "type": "rectangle", "text": "Element Plus\nUI组件库", "position": {"x": 1080, "y": 490}, "size": {"width": 80, "height": 40}, "style": {"fill": "#BBDEFB", "stroke": "#2196F3", "strokeWidth": 1}}]}, {"id": "monitor-layer", "name": "监控运维层", "color": "#FCE4EC", "border": "#880E4F", "position": {"x": 1200, "y": 50}, "size": {"width": 300, "height": 300}, "elements": [{"id": "zabbix", "type": "rectangle", "text": "Zabbix\n系统监控", "position": {"x": 1240, "y": 90}, "size": {"width": 80, "height": 50}, "style": {"fill": "#F8BBD9", "stroke": "#C2185B", "strokeWidth": 2}}, {"id": "grafana", "type": "rectangle", "text": "Grafana\n监控面板", "position": {"x": 1340, "y": 90}, "size": {"width": 80, "height": 50}, "style": {"fill": "#F8BBD9", "stroke": "#C2185B", "strokeWidth": 2}}, {"id": "elk-stack", "type": "rectangle", "text": "ELK Stack\n日志管理", "position": {"x": 1240, "y": 160}, "size": {"width": 80, "height": 50}, "style": {"fill": "#F8BBD9", "stroke": "#C2185B", "strokeWidth": 2}}, {"id": "docker", "type": "rectangle", "text": "Docker\n容器化", "position": {"x": 1340, "y": 160}, "size": {"width": 80, "height": 50}, "style": {"fill": "#F8BBD9", "stroke": "#C2185B", "strokeWidth": 2}}]}, {"id": "external-layer", "name": "外部服务层", "color": "#F1F8E9", "border": "#33691E", "position": {"x": 1200, "y": 400}, "size": {"width": 300, "height": 200}, "elements": [{"id": "sms-service", "type": "rectangle", "text": "短信服务\n告警通知", "position": {"x": 1240, "y": 440}, "size": {"width": 80, "height": 50}, "style": {"fill": "#C5E1A5", "stroke": "#689F38", "strokeWidth": 2}}, {"id": "email-service", "type": "rectangle", "text": "邮件服务\nSMTP推送", "position": {"x": 1340, "y": 440}, "size": {"width": 80, "height": 50}, "style": {"fill": "#C5E1A5", "stroke": "#689F38", "strokeWidth": 2}}, {"id": "map-service", "type": "rectangle", "text": "地图服务\n设备定位", "position": {"x": 1240, "y": 510}, "size": {"width": 80, "height": 50}, "style": {"fill": "#C5E1A5", "stroke": "#689F38", "strokeWidth": 2}}, {"id": "cloud-service", "type": "rectangle", "text": "云服务\n阿里云/腾讯云", "position": {"x": 1340, "y": 510}, "size": {"width": 80, "height": 50}, "style": {"fill": "#C5E1A5", "stroke": "#689F38", "strokeWidth": 2}}]}], "connections": [{"id": "device-to-emqx-1", "from": "oil-well-1", "to": "emqx-broker", "style": {"stroke": "#7B1FA2", "strokeWidth": 2, "strokeDasharray": "5,5"}, "label": "MQTT发布"}, {"id": "device-to-emqx-2", "from": "oil-well-2", "to": "emqx-broker", "style": {"stroke": "#7B1FA2", "strokeWidth": 2, "strokeDasharray": "5,5"}, "label": "MQTT发布"}, {"id": "device-to-emqx-n", "from": "oil-well-n", "to": "emqx-broker", "style": {"stroke": "#7B1FA2", "strokeWidth": 2, "strokeDasharray": "5,5"}, "label": "MQTT发布"}, {"id": "emqx-to-nginx", "from": "emqx-broker", "to": "nginx-lb", "style": {"stroke": "#4CAF50", "strokeWidth": 2}, "label": "消息转发"}, {"id": "nginx-to-php1", "from": "nginx-lb", "to": "php-swoole-1", "style": {"stroke": "#4CAF50", "strokeWidth": 2}, "label": "负载均衡"}, {"id": "nginx-to-php2", "from": "nginx-lb", "to": "php-swoole-2", "style": {"stroke": "#4CAF50", "strokeWidth": 2}, "label": "负载均衡"}, {"id": "nginx-to-ws", "from": "nginx-lb", "to": "websocket-server", "style": {"stroke": "#4CAF50", "strokeWidth": 2}, "label": "负载均衡"}, {"id": "php-to-mysql", "from": "php-swoole-1", "to": "mysql-cluster", "style": {"stroke": "#F57C00", "strokeWidth": 2}, "label": "数据存储"}, {"id": "php-to-redis", "from": "php-swoole-2", "to": "redis-cluster", "style": {"stroke": "#F57C00", "strokeWidth": 2}, "label": "缓存操作"}, {"id": "ws-to-es", "from": "websocket-server", "to": "elasticsearch", "style": {"stroke": "#F57C00", "strokeWidth": 2}, "label": "索引数据"}, {"id": "vue-to-nginx", "from": "vue3-app", "to": "nginx-lb", "style": {"stroke": "#1976D2", "strokeWidth": 2}, "label": "HTTP/HTTPS"}, {"id": "vue-to-ws", "from": "vue3-app", "to": "websocket-server", "style": {"stroke": "#1976D2", "strokeWidth": 2, "strokeDasharray": "5,5"}, "label": "WebSocket"}, {"id": "monitor-to-backend", "from": "zabbix", "to": "nginx-lb", "style": {"stroke": "#C2185B", "strokeWidth": 1, "strokeDasharray": "3,3"}, "label": "监控"}, {"id": "backend-to-sms", "from": "swoole-processes", "to": "sms-service", "style": {"stroke": "#689F38", "strokeWidth": 1}, "label": "告警通知"}]}