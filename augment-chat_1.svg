<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 7153.34765625 2245" style="max-width: 7153.34765625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855"><style>#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .error-icon{fill:#a44141;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .edge-thickness-normal{stroke-width:1px;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .marker.cross{stroke:lightgrey;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 p{margin:0;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .cluster-label text{fill:#F9FFFE;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .cluster-label span{color:#F9FFFE;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .cluster-label span p{background-color:transparent;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .label text,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 span{fill:#ccc;color:#ccc;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .node rect,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .node circle,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .node ellipse,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .node polygon,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .rough-node .label text,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .node .label text,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .image-shape .label,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .icon-shape .label{text-anchor:middle;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .rough-node .label,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .node .label,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .image-shape .label,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .icon-shape .label{text-align:center;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .node.clickable{cursor:pointer;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .arrowheadPath{fill:lightgrey;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .cluster text{fill:#F9FFFE;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .cluster span{color:#F9FFFE;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 rect.text{fill:none;stroke-width:0;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .icon-shape,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .icon-shape p,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .icon-shape rect,#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .deviceClass&gt;*{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .deviceClass span{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .commClass&gt;*{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .commClass span{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .backendClass&gt;*{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .backendClass span{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .dataClass&gt;*{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .dataClass span{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .frontendClass&gt;*{fill:#e3f2fd!important;stroke:#0d47a1!important;stroke-width:2px!important;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .frontendClass span{fill:#e3f2fd!important;stroke:#0d47a1!important;stroke-width:2px!important;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .devopsClass&gt;*{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .devopsClass span{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .externalClass&gt;*{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855 .externalClass span{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph24" class="cluster"><rect height="178" width="2036.01953125" y="1881" x="397.4609375" style=""></rect><g transform="translate(1315.470703125, 1881)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>外部服务层 (External Services)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph23" class="cluster"><rect height="554" width="3678.60546875" y="187" x="250.35546875" style=""></rect><g transform="translate(1993.017578125, 187)" class="cluster-label"><foreignObject height="24" width="193.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>监控运维层 (DevOps Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph19" class="cluster"><rect height="1189" width="1998.96875" y="187" x="3948.9609375" style=""></rect><g transform="translate(4848.4453125, 187)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>前端应用层 (Frontend Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph13" class="cluster"><rect height="356" width="3072.19921875" y="1881" x="2839.69140625" style=""></rect><g transform="translate(4281.603515625, 1881)" class="cluster-label"><foreignObject height="24" width="188.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>数据存储层 (Data Storage)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph8" class="cluster"><rect height="1016" width="3712.1640625" y="815" x="8" style=""></rect><g transform="translate(1764.08203125, 815)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>后端服务层 (Backend Services)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="561" width="639.59375" y="815" x="6465.2109375" style=""></rect><g transform="translate(6685.0078125, 815)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>通信层 (Communication Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="733" width="1177.41796875" y="8" x="5967.9296875" style=""></rect><g transform="translate(6478.474609375, 8)" class="cluster-label"><foreignObject height="24" width="156.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设备层 (Device Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="日志管理" class="cluster"><rect height="504" width="665.9375" y="212" x="549.77734375" style=""></rect><g transform="translate(850.74609375, 212)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>日志管理</p></span></div></foreignObject></g></g><g data-look="classic" id="系统监控" class="cluster"><rect height="504" width="2673.24609375" y="212" x="1235.71484375" style=""></rect><g transform="translate(2540.337890625, 212)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>系统监控</p></span></div></foreignObject></g></g><g data-look="classic" id="移动端" class="cluster"><rect height="511" width="210" y="840" x="3968.9609375" style=""></rect><g transform="translate(4049.9609375, 840)" class="cluster-label"><foreignObject height="24" width="48"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>移动端</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph17" class="cluster"><rect height="1139" width="1728.96875" y="212" x="4198.9609375" style=""></rect><g transform="translate(5010.2421875, 212)" class="cluster-label"><foreignObject height="24" width="106.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Vue 3 Web应用</p></span></div></foreignObject></g></g><g data-look="classic" id="功能模块" class="cluster"><rect height="454" width="1212.96875" y="237" x="4218.9609375" style=""></rect><g transform="translate(4793.4453125, 237)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>功能模块</p></span></div></foreignObject></g></g><g data-look="classic" id="样式系统" class="cluster"><rect height="454" width="210" y="237" x="5451.9296875" style=""></rect><g transform="translate(5524.9296875, 237)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>样式系统</p></span></div></foreignObject></g></g><g data-look="classic" id="状态管理" class="cluster"><rect height="454" width="226" y="237" x="5681.9296875" style=""></rect><g transform="translate(5762.9296875, 237)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>状态管理</p></span></div></foreignObject></g></g><g data-look="classic" id="文件存储" class="cluster"><rect height="128" width="242.78125" y="1906" x="3685.75390625" style=""></rect><g transform="translate(3775.14453125, 1906)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>文件存储</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph11" class="cluster"><rect height="128" width="766.109375" y="1906" x="2859.69140625" style=""></rect><g transform="translate(3177.58984375, 1906)" class="cluster-label"><foreignObject height="24" width="130.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Elasticsearch 集群</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph10" class="cluster"><rect height="306" width="1103.0625" y="1906" x="4772.828125" style=""></rect><g transform="translate(5287.2734375, 1906)" class="cluster-label"><foreignObject height="24" width="74.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis 集群</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph9" class="cluster"><rect height="306" width="600.30859375" y="1906" x="3948.53515625" style=""></rect><g transform="translate(4201.041015625, 1906)" class="cluster-label"><foreignObject height="24" width="95.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MySQL 8 集群</p></span></div></foreignObject></g></g><g data-look="classic" id="负载均衡" class="cluster"><rect height="153" width="266.390625" y="1450" x="3408.921875" style=""></rect><g transform="translate(3510.1171875, 1450)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>负载均衡</p></span></div></foreignObject></g></g><g data-look="classic" id="Linux服务器集群" class="cluster"><rect height="966" width="3099.828125" y="840" x="289.09375" style=""></rect><g transform="translate(1779.921875, 840)" class="cluster-label"><foreignObject height="24" width="118.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Linux服务器集群</p></span></div></foreignObject></g></g><g data-look="classic" id="Swoole进程管理" class="cluster"><rect height="713" width="1459.30078125" y="865" x="1455.62890625" style=""></rect><g transform="translate(2128.169921875, 865)" class="cluster-label"><foreignObject height="24" width="114.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Swoole进程管理</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="128" width="1819.56640625" y="1653" x="1324.4921875" style=""></rect><g transform="translate(2153.986328125, 1653)" class="cluster-label"><foreignObject height="24" width="160.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PHP 8.3 + Swoole 应用</p></span></div></foreignObject></g></g><g data-look="classic" id="设备数据" class="cluster"><rect height="104" width="898.390625" y="33" x="5987.9296875" style=""></rect><g transform="translate(6405.125, 33)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设备数据</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OW1_EMQX_0" d="M6599.371,503L6599.371,534.333C6599.371,565.667,6599.371,628.333,6599.371,663.833C6599.371,699.333,6599.371,707.667,6599.371,716C6599.371,724.333,6599.371,732.667,6599.371,743C6599.371,753.333,6599.371,765.667,6599.371,778C6599.371,790.333,6599.371,802.667,6599.371,813C6599.371,823.333,6599.371,831.667,6599.371,840C6599.371,848.333,6599.371,856.667,6643.835,894.431C6688.298,932.196,6777.226,999.392,6821.689,1032.99L6866.153,1066.589"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OW2_EMQX_1" d="M6813.762,503L6813.762,534.333C6813.762,565.667,6813.762,628.333,6813.762,663.833C6813.762,699.333,6813.762,707.667,6813.762,716C6813.762,724.333,6813.762,732.667,6813.762,743C6813.762,753.333,6813.762,765.667,6813.762,778C6813.762,790.333,6813.762,802.667,6813.762,813C6813.762,823.333,6813.762,831.667,6813.762,840C6813.762,848.333,6813.762,856.667,6828.491,894.223C6843.221,931.78,6872.68,998.56,6887.409,1031.95L6902.138,1065.34"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OW3_EMQX_2" d="M7028.152,503L7028.152,534.333C7028.152,565.667,7028.152,628.333,7028.152,663.833C7028.152,699.333,7028.152,707.667,7028.152,716C7028.152,724.333,7028.152,732.667,7028.152,743C7028.152,753.333,7028.152,765.667,7028.152,778C7028.152,790.333,7028.152,802.667,7028.152,813C7028.152,823.333,7028.152,831.667,7028.152,840C7028.152,848.333,7028.152,856.667,7013.423,894.223C6998.693,931.78,6969.235,998.56,6954.505,1031.95L6939.776,1065.34"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D1_OW1_3" d="M6089.125,112L6089.125,116.167C6089.125,120.333,6089.125,128.667,6089.125,137C6089.125,145.333,6089.125,153.667,6089.125,162C6089.125,170.333,6089.125,178.667,6089.125,187C6089.125,195.333,6089.125,203.667,6089.125,212C6089.125,220.333,6089.125,228.667,6159.858,264.301C6230.59,299.936,6372.056,362.871,6442.788,394.339L6513.521,425.807"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2_OW1_4" d="M6267.32,112L6267.32,116.167C6267.32,120.333,6267.32,128.667,6267.32,137C6267.32,145.333,6267.32,153.667,6267.32,162C6267.32,170.333,6267.32,178.667,6267.32,187C6267.32,195.333,6267.32,203.667,6267.32,212C6267.32,220.333,6267.32,228.667,6312.604,263.79C6357.887,298.914,6448.454,360.828,6493.737,391.785L6539.021,422.743"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D3_OW1_5" d="M6441.32,112L6441.32,116.167C6441.32,120.333,6441.32,128.667,6441.32,137C6441.32,145.333,6441.32,153.667,6441.32,162C6441.32,170.333,6441.32,178.667,6441.32,187C6441.32,195.333,6441.32,203.667,6441.32,212C6441.32,220.333,6441.32,228.667,6462.755,263.62C6484.191,298.572,6527.061,360.145,6548.496,390.931L6569.931,421.717"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D4_OW1_6" d="M6615.32,112L6615.32,116.167C6615.32,120.333,6615.32,128.667,6615.32,137C6615.32,145.333,6615.32,153.667,6615.32,162C6615.32,170.333,6615.32,178.667,6615.32,187C6615.32,195.333,6615.32,203.667,6615.32,212C6615.32,220.333,6615.32,228.667,6613.166,263.502C6611.011,298.337,6606.701,359.673,6604.546,390.342L6602.392,421.01"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D5_OW1_7" d="M6789.32,112L6789.32,116.167C6789.32,120.333,6789.32,128.667,6789.32,137C6789.32,145.333,6789.32,153.667,6789.32,162C6789.32,170.333,6789.32,178.667,6789.32,187C6789.32,195.333,6789.32,203.667,6789.32,212C6789.32,220.333,6789.32,228.667,6763.529,263.655C6737.738,298.644,6686.155,360.288,6660.364,391.11L6634.573,421.932"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMQX_WORKER2_8" d="M6922.562,1147L6923.961,1181C6925.36,1215,6928.159,1283,6929.558,1321.167C6930.957,1359.333,6930.957,1367.667,6252.223,1378C5573.49,1388.333,4216.022,1400.667,3537.288,1413C2858.555,1425.333,2858.555,1437.667,2852.073,1447.661C2845.591,1457.655,2832.628,1465.311,2826.147,1469.138L2819.665,1472.966"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMQX_WS1_9" d="M6909.897,1147L6900.256,1181C6890.614,1215,6871.33,1283,6861.689,1321.167C6852.047,1359.333,6852.047,1367.667,6250.514,1378C5648.982,1388.333,4445.917,1400.667,3844.384,1413C3242.852,1425.333,3242.852,1437.667,3242.852,1454.5C3242.852,1471.333,3242.852,1492.667,3242.852,1514C3242.852,1535.333,3242.852,1556.667,3242.852,1571.5C3242.852,1586.333,3242.852,1594.667,3242.852,1603C3242.852,1611.333,3242.852,1619.667,3195.217,1628C3147.582,1636.333,3052.313,1644.667,3007.816,1652.494C2963.32,1660.321,2969.598,1667.642,2972.736,1671.303L2975.875,1674.963"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EMQX_WS2_10" d="M6854.232,1147L6796.062,1181C6737.892,1215,6621.551,1283,6563.381,1321.167C6505.211,1359.333,6505.211,1367.667,5932.97,1378C5360.729,1388.333,4216.247,1400.667,3644.007,1413C3071.766,1425.333,3071.766,1437.667,3071.766,1454.5C3071.766,1471.333,3071.766,1492.667,3071.766,1514C3071.766,1535.333,3071.766,1556.667,3071.766,1571.5C3071.766,1586.333,3071.766,1594.667,3071.766,1603C3071.766,1611.333,3071.766,1619.667,3011.932,1628C2952.098,1636.333,2832.43,1644.667,2775.734,1652.494C2719.039,1660.321,2725.316,1667.642,2728.455,1671.303L2731.594,1674.963"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MASTER_WORKER1_11" d="M1931.563,1143.722L1999.032,1178.268C2066.501,1212.815,2201.44,1281.907,2268.91,1320.62C2336.379,1359.333,2336.379,1367.667,2336.379,1378C2336.379,1388.333,2336.379,1400.667,2336.379,1413C2336.379,1425.333,2336.379,1437.667,2336.379,1447.333C2336.379,1457,2336.379,1464,2336.379,1467.5L2336.379,1471"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MASTER_WORKER2_12" d="M1904.772,1147L1942.238,1181C1979.704,1215,2054.635,1283,2092.101,1321.167C2129.566,1359.333,2129.566,1367.667,2129.566,1378C2129.566,1388.333,2129.566,1400.667,2129.566,1413C2129.566,1425.333,2129.566,1437.667,2219.746,1453.133C2309.926,1468.599,2490.286,1487.199,2580.466,1496.498L2670.646,1505.798"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MASTER_WORKER3_13" d="M1887.88,1147L1910.62,1181C1933.359,1215,1978.838,1283,2001.577,1321.167C2024.316,1359.333,2024.316,1367.667,2024.316,1378C2024.316,1388.333,2024.316,1400.667,2024.316,1413C2024.316,1425.333,2024.316,1437.667,2024.316,1447.333C2024.316,1457,2024.316,1464,2024.316,1467.5L2024.316,1471"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MASTER_TASK_14" d="M1812.8,1147L1770.086,1181C1727.371,1215,1641.941,1283,1599.227,1321.167C1556.512,1359.333,1556.512,1367.667,1556.512,1378C1556.512,1388.333,1556.512,1400.667,1556.512,1413C1556.512,1425.333,1556.512,1437.667,1556.512,1447.333C1556.512,1457,1556.512,1464,1556.512,1467.5L1556.512,1471"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WORKER1_API1_15" d="M2391.561,1553L2397.456,1557.167C2403.352,1561.333,2415.143,1569.667,2421.038,1578C2426.934,1586.333,2426.934,1594.667,2426.934,1603C2426.934,1611.333,2426.934,1619.667,2426.934,1628C2426.934,1636.333,2426.934,1644.667,2415.808,1653.784C2404.683,1662.902,2382.432,1672.804,2371.307,1677.755L2360.182,1682.706"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WORKER1_API2_16" d="M2325.501,1553L2324.338,1557.167C2323.176,1561.333,2320.852,1569.667,2319.69,1578C2318.527,1586.333,2318.527,1594.667,2318.527,1603C2318.527,1611.333,2318.527,1619.667,2318.527,1628C2318.527,1636.333,2318.527,1644.667,2292.701,1655.951C2266.875,1667.235,2215.223,1681.471,2189.397,1688.589L2163.571,1695.706"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WORKER1_API3_17" d="M2260.824,1538.569L2240.615,1545.141C2220.405,1551.713,2179.986,1564.856,2159.776,1575.595C2139.566,1586.333,2139.566,1594.667,2139.566,1603C2139.566,1611.333,2139.566,1619.667,2139.566,1628C2139.566,1636.333,2139.566,1644.667,2110.768,1656.204C2081.97,1667.741,2024.374,1682.481,1995.576,1689.851L1966.777,1697.222"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WORKER3_WS1_18" d="M2082.359,1553L2088.561,1557.167C2094.762,1561.333,2107.164,1569.667,2113.365,1578C2119.566,1586.333,2119.566,1594.667,2119.566,1603C2119.566,1611.333,2119.566,1619.667,2119.566,1628C2119.566,1636.333,2119.566,1644.667,2251.437,1658.291C2383.307,1671.916,2647.047,1690.831,2778.917,1700.289L2910.788,1709.747"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WORKER3_WS2_19" d="M1970.403,1553L1964.643,1557.167C1958.884,1561.333,1947.364,1569.667,1941.604,1578C1935.844,1586.333,1935.844,1594.667,1935.844,1603C1935.844,1611.333,1935.844,1619.667,1935.844,1628C1935.844,1636.333,1935.844,1644.667,2057.621,1658.203C2179.398,1671.74,2422.953,1690.479,2544.731,1699.849L2666.508,1709.219"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NGINX_API1_20" d="M3576.606,1553L3580.291,1557.167C3583.976,1561.333,3591.345,1569.667,3595.03,1578C3598.715,1586.333,3598.715,1594.667,3557.083,1603C3515.451,1611.333,3432.186,1619.667,3342.919,1628C3253.652,1636.333,3158.383,1644.667,2993.648,1658.442C2828.914,1672.217,2594.714,1691.433,2477.614,1701.041L2360.514,1710.65"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NGINX_API2_21" d="M3560.398,1553L3562.352,1557.167C3564.305,1561.333,3568.211,1569.667,3570.164,1578C3572.117,1586.333,3572.117,1594.667,3531.585,1603C3491.052,1611.333,3409.987,1619.667,3321.82,1628C3233.652,1636.333,3138.383,1644.667,2944.18,1658.637C2749.977,1672.608,2456.842,1692.215,2310.274,1702.019L2163.706,1711.823"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NGINX_API3_22" d="M3548.211,1553L3548.862,1557.167C3549.513,1561.333,3550.815,1569.667,3551.466,1578C3552.117,1586.333,3552.117,1594.667,3511.585,1603C3471.052,1611.333,3389.987,1619.667,3301.82,1628C3213.652,1636.333,3118.383,1644.667,2894.712,1658.772C2671.041,1672.877,2318.968,1692.754,2142.932,1702.692L1966.896,1712.63"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NGINX_WS1_23" d="M3536.023,1553L3535.372,1557.167C3534.721,1561.333,3533.419,1569.667,3532.768,1578C3532.117,1586.333,3532.117,1594.667,3491.585,1603C3451.052,1611.333,3369.987,1619.667,3281.82,1628C3193.652,1636.333,3098.383,1644.667,3051.23,1652.34C3004.078,1660.012,3005.043,1667.025,3005.525,1670.531L3006.007,1674.037"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NGINX_WS2_24" d="M3513.722,1553L3510.688,1557.167C3507.654,1561.333,3501.587,1569.667,3498.553,1578C3495.52,1586.333,3495.52,1594.667,3457.753,1603C3419.987,1611.333,3344.454,1619.667,3259.053,1628C3173.652,1636.333,3078.383,1644.667,3011.664,1654.501C2944.946,1664.336,2906.779,1675.673,2887.695,1681.341L2868.612,1687.009"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API1_MYSQL_M_25" d="M2356.527,1750.66L2367.555,1755.716C2378.583,1760.773,2400.639,1770.887,2411.667,1780.11C2422.695,1789.333,2422.695,1797.667,2422.695,1806C2422.695,1814.333,2422.695,1822.667,2422.695,1831C2422.695,1839.333,2422.695,1847.667,2740.214,1856C3057.732,1864.333,3692.768,1872.667,4010.286,1881C4327.805,1889.333,4327.805,1897.667,4332.985,1905.607C4338.165,1913.548,4348.525,1921.096,4353.706,1924.87L4358.886,1928.645"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API2_MYSQL_S1_26" d="M2139.229,1756L2144.883,1760.167C2150.537,1764.333,2161.845,1772.667,2167.498,1781C2173.152,1789.333,2173.152,1797.667,2173.152,1806C2173.152,1814.333,2173.152,1822.667,2173.152,1831C2173.152,1839.333,2173.152,1847.667,2477.25,1856C2781.348,1864.333,3389.543,1872.667,3731.735,1881C4073.927,1889.333,4150.116,1897.667,4188.21,1912.5C4226.305,1927.333,4226.305,1948.667,4226.305,1970C4226.305,1991.333,4226.305,2012.667,4226.305,2027.5C4226.305,2042.333,4226.305,2050.667,4226.305,2059C4226.305,2067.333,4226.305,2075.667,4233.705,2083.692C4241.105,2091.717,4255.905,2099.434,4263.305,2103.292L4270.706,2107.151"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API3_MYSQL_S2_27" d="M1937.275,1756L1942.379,1760.167C1947.484,1764.333,1957.693,1772.667,1962.798,1781C1967.902,1789.333,1967.902,1797.667,1967.902,1806C1967.902,1814.333,1967.902,1822.667,1967.902,1831C1967.902,1839.333,1967.902,1847.667,2264.913,1856C2561.924,1864.333,3155.947,1872.667,3499.122,1881C3842.298,1889.333,3934.628,1897.667,3980.792,1912.5C4026.957,1927.333,4026.957,1948.667,4026.957,1970C4026.957,1991.333,4026.957,2012.667,4026.957,2027.5C4026.957,2042.333,4026.957,2050.667,4026.957,2059C4026.957,2067.333,4026.957,2075.667,4033.441,2083.661C4039.924,2091.655,4052.891,2099.311,4059.375,2103.139L4065.859,2106.966"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WORKER2_REDIS_M_28" d="M2750.18,1553L2750.18,1557.167C2750.18,1561.333,2750.18,1569.667,2750.18,1578C2750.18,1586.333,2750.18,1594.667,2750.18,1603C2750.18,1611.333,2750.18,1619.667,2819.16,1628C2888.139,1636.333,3026.099,1644.667,3095.079,1659.5C3164.059,1674.333,3164.059,1695.667,3164.059,1717C3164.059,1738.333,3164.059,1759.667,3164.059,1774.5C3164.059,1789.333,3164.059,1797.667,3164.059,1806C3164.059,1814.333,3164.059,1822.667,3164.059,1831C3164.059,1839.333,3164.059,1847.667,3464.4,1856C3764.741,1864.333,4365.423,1872.667,4701.075,1881C5036.727,1889.333,5107.348,1897.667,5175.629,1909.377C5243.911,1921.087,5309.854,1936.175,5342.825,1943.718L5375.796,1951.262"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WS1_REDIS_S1_29" d="M3011.918,1756L3011.918,1760.167C3011.918,1764.333,3011.918,1772.667,3011.918,1781C3011.918,1789.333,3011.918,1797.667,3011.918,1806C3011.918,1814.333,3011.918,1822.667,3011.918,1831C3011.918,1839.333,3011.918,1847.667,3314.283,1856C3616.647,1864.333,4221.376,1872.667,4559.051,1881C4896.727,1889.333,4967.348,1897.667,5002.658,1912.5C5037.969,1927.333,5037.969,1948.667,5037.969,1970C5037.969,1991.333,5037.969,2012.667,5037.969,2027.5C5037.969,2042.333,5037.969,2050.667,5037.969,2059C5037.969,2067.333,5037.969,2075.667,5084.743,2088.06C5131.518,2100.454,5225.066,2116.908,5271.841,2125.135L5318.615,2133.362"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WS2_REDIS_S2_30" d="M2767.637,1756L2767.637,1760.167C2767.637,1764.333,2767.637,1772.667,2767.637,1781C2767.637,1789.333,2767.637,1797.667,2767.637,1806C2767.637,1814.333,2767.637,1822.667,2767.637,1831C2767.637,1839.333,2767.637,1847.667,3088.691,1856C3409.746,1864.333,4051.855,1872.667,4405.101,1881C4758.346,1889.333,4822.728,1897.667,4854.919,1912.5C4887.109,1927.333,4887.109,1948.667,4887.109,1970C4887.109,1991.333,4887.109,2012.667,4887.109,2027.5C4887.109,2042.333,4887.109,2050.667,4887.109,2059C4887.109,2067.333,4887.109,2075.667,4887.109,2083.333C4887.109,2091,4887.109,2098,4887.109,2101.5L4887.109,2105"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API2_ES1_31" d="M2048.532,1756L2044.496,1760.167C2040.46,1764.333,2032.388,1772.667,2028.352,1781C2024.316,1789.333,2024.316,1797.667,2024.316,1806C2024.316,1814.333,2024.316,1822.667,2024.316,1831C2024.316,1839.333,2024.316,1847.667,2316.925,1856C2609.534,1864.333,3194.751,1872.667,3439.273,1881C3683.796,1889.333,3587.622,1897.667,3539.536,1905.333C3491.449,1913,3491.449,1920,3491.449,1923.5L3491.449,1927"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API3_ES2_32" d="M1850.786,1756L1846.651,1760.167C1842.515,1764.333,1834.244,1772.667,1830.108,1781C1825.973,1789.333,1825.973,1797.667,1825.973,1806C1825.973,1814.333,1825.973,1822.667,1825.973,1831C1825.973,1839.333,1825.973,1847.667,2114.257,1856C2402.542,1864.333,2979.111,1872.667,3215.24,1881C3451.368,1889.333,3347.057,1897.667,3294.902,1905.333C3242.746,1913,3242.746,1920,3242.746,1923.5L3242.746,1927"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TASK_ES3_33" d="M1616.423,1553L1622.824,1557.167C1629.225,1561.333,1642.027,1569.667,1648.427,1578C1654.828,1586.333,1654.828,1594.667,1654.828,1603C1654.828,1611.333,1654.828,1619.667,1544.59,1628C1434.353,1636.333,1213.878,1644.667,1103.64,1659.5C993.402,1674.333,993.402,1695.667,993.402,1717C993.402,1738.333,993.402,1759.667,993.402,1774.5C993.402,1789.333,993.402,1797.667,993.402,1806C993.402,1814.333,993.402,1822.667,993.402,1831C993.402,1839.333,993.402,1847.667,1326.842,1856C1660.283,1864.333,2327.163,1872.667,2660.603,1881C2994.043,1889.333,2994.043,1897.667,2994.043,1905.333C2994.043,1913,2994.043,1920,2994.043,1923.5L2994.043,1927"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API1_FS_34" d="M2229.249,1756L2223.493,1760.167C2217.737,1764.333,2206.226,1772.667,2200.47,1781C2194.715,1789.333,2194.715,1797.667,2194.715,1806C2194.715,1814.333,2194.715,1822.667,2194.715,1831C2194.715,1839.333,2194.715,1847.667,2506.497,1856C2818.28,1864.333,3441.845,1872.667,3720.823,1881C3999.801,1889.333,3934.191,1897.667,3897.849,1905.519C3861.506,1913.371,3854.429,1920.743,3850.891,1924.429L3847.353,1928.114"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API3_FS_35" d="M1816.09,1741.294L1796.094,1747.911C1776.098,1754.529,1736.105,1767.765,1716.109,1778.549C1696.113,1789.333,1696.113,1797.667,1696.113,1806C1696.113,1814.333,1696.113,1822.667,1696.113,1831C1696.113,1839.333,1696.113,1847.667,1995.923,1856C2295.732,1864.333,2895.35,1872.667,3240.53,1881C3585.71,1889.333,3676.451,1897.667,3724.069,1905.434C3771.688,1913.202,3776.184,1920.405,3778.432,1924.006L3780.68,1927.607"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MYSQL_M_MYSQL_S1_36" d="M4457.767,2009L4462.267,2013.167C4466.766,2017.333,4475.766,2025.667,4480.266,2034C4484.766,2042.333,4484.766,2050.667,4484.766,2059C4484.766,2067.333,4484.766,2075.667,4476.39,2083.783C4468.015,2091.899,4451.263,2099.799,4442.888,2103.749L4434.512,2107.698"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MYSQL_M_MYSQL_S2_37" d="M4358.17,2009L4352.029,2013.167C4345.888,2017.333,4333.606,2025.667,4327.465,2034C4321.324,2042.333,4321.324,2050.667,4321.324,2059C4321.324,2067.333,4321.324,2075.667,4304.602,2085.589C4287.879,2095.51,4254.434,2107.021,4237.712,2112.776L4220.989,2118.531"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_REDIS_M_REDIS_S1_38" d="M5535.695,1984.22L5581.204,1992.517C5626.714,2000.813,5717.732,2017.407,5763.241,2029.87C5808.75,2042.333,5808.75,2050.667,5808.75,2059C5808.75,2067.333,5808.75,2075.667,5754.803,2088.318C5700.856,2100.97,5592.962,2117.939,5539.016,2126.424L5485.069,2134.909"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_REDIS_M_REDIS_S2_39" d="M5379.695,1989.011L5348.931,1996.509C5318.167,2004.007,5256.638,2019.004,5225.874,2030.668C5195.109,2042.333,5195.109,2050.667,5195.109,2059C5195.109,2067.333,5195.109,2075.667,5157.642,2087.619C5120.175,2099.571,5045.241,2115.141,5007.774,2122.927L4970.307,2130.712"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_VUE_APP_NGINX_40" d="M5304.329,1147L5364.2,1181C5424.072,1215,5543.815,1283,5603.687,1321.167C5663.559,1359.333,5663.559,1367.667,5328.657,1378C4993.755,1388.333,4323.952,1400.667,3989.05,1413C3654.148,1425.333,3654.148,1437.667,3647.434,1447.669C3640.719,1457.672,3627.289,1465.344,3620.574,1469.18L3613.859,1473.016"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_VUE_APP_WS1_41" d="M5257.624,1147L5276.778,1181C5295.933,1215,5334.242,1283,5353.396,1321.167C5372.551,1359.333,5372.551,1367.667,4972.771,1378C4572.992,1388.333,3773.434,1400.667,3373.654,1413C2973.875,1425.333,2973.875,1437.667,2973.875,1454.5C2973.875,1471.333,2973.875,1492.667,2973.875,1514C2973.875,1535.333,2973.875,1556.667,2973.875,1571.5C2973.875,1586.333,2973.875,1594.667,2973.875,1603C2973.875,1611.333,2973.875,1619.667,2904.693,1628C2835.51,1636.333,2697.146,1644.667,2686.636,1657.12C2676.126,1669.574,2793.472,1686.147,2852.144,1694.434L2910.817,1702.721"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MOBILE_NGINX_42" d="M4073.961,1147L4073.961,1181C4073.961,1215,4073.961,1283,4073.961,1321.167C4073.961,1359.333,4073.961,1367.667,3985.32,1378C3896.68,1388.333,3719.398,1400.667,3630.758,1413C3542.117,1425.333,3542.117,1437.667,3542.117,1447.333C3542.117,1457,3542.117,1464,3542.117,1467.5L3542.117,1471"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PINIA_VUE_APP_43" d="M5794.93,503L5794.93,534.333C5794.93,565.667,5794.93,628.333,5794.93,663.833C5794.93,699.333,5794.93,707.667,5794.93,716C5794.93,724.333,5794.93,732.667,5794.93,743C5794.93,753.333,5794.93,765.667,5794.93,778C5794.93,790.333,5794.93,802.667,5794.93,813C5794.93,823.333,5794.93,831.667,5794.93,840C5794.93,848.333,5794.93,856.667,5717.288,894.568C5639.647,932.469,5484.364,999.937,5406.723,1033.672L5329.082,1067.406"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SASS_VUE_APP_44" d="M5556.93,503L5556.93,534.333C5556.93,565.667,5556.93,628.333,5556.93,663.833C5556.93,699.333,5556.93,707.667,5556.93,716C5556.93,724.333,5556.93,732.667,5556.93,743C5556.93,753.333,5556.93,765.667,5556.93,778C5556.93,790.333,5556.93,802.667,5556.93,813C5556.93,823.333,5556.93,831.667,5556.93,840C5556.93,848.333,5556.93,856.667,5512.509,894.431C5468.088,932.196,5379.247,999.391,5334.826,1032.989L5290.406,1066.587"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F1_VUE_APP_45" d="M4331.961,503L4331.961,534.333C4331.961,565.667,4331.961,628.333,4331.961,663.833C4331.961,699.333,4331.961,707.667,4331.961,716C4331.961,724.333,4331.961,732.667,4331.961,743C4331.961,753.333,4331.961,765.667,4331.961,778C4331.961,790.333,4331.961,802.667,4331.961,813C4331.961,823.333,4331.961,831.667,4331.961,840C4331.961,848.333,4331.961,856.667,4460.327,895.351C4588.693,934.034,4845.425,1003.069,4973.791,1037.586L5102.157,1072.103"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F2_VUE_APP_46" d="M4537.961,503L4537.961,534.333C4537.961,565.667,4537.961,628.333,4537.961,663.833C4537.961,699.333,4537.961,707.667,4537.961,716C4537.961,724.333,4537.961,732.667,4537.961,743C4537.961,753.333,4537.961,765.667,4537.961,778C4537.961,790.333,4537.961,802.667,4537.961,813C4537.961,823.333,4537.961,831.667,4537.961,840C4537.961,848.333,4537.961,856.667,4634.951,894.614C4731.941,932.561,4925.92,1000.123,5022.91,1033.904L5119.9,1067.684"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F3_VUE_APP_47" d="M4750.906,503L4750.906,534.333C4750.906,565.667,4750.906,628.333,4750.906,663.833C4750.906,699.333,4750.906,707.667,4750.906,716C4750.906,724.333,4750.906,732.667,4750.906,743C4750.906,753.333,4750.906,765.667,4750.906,778C4750.906,790.333,4750.906,802.667,4750.906,813C4750.906,823.333,4750.906,831.667,4750.906,840C4750.906,848.333,4750.906,856.667,4818.135,894.535C4885.363,932.402,5019.821,999.805,5087.049,1033.506L5154.278,1067.207"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F4_VUE_APP_48" d="M4947.852,503L4947.852,534.333C4947.852,565.667,4947.852,628.333,4947.852,663.833C4947.852,699.333,4947.852,707.667,4947.852,716C4947.852,724.333,4947.852,732.667,4947.852,743C4947.852,753.333,4947.852,765.667,4947.852,778C4947.852,790.333,4947.852,802.667,4947.852,813C4947.852,823.333,4947.852,831.667,4947.852,840C4947.852,848.333,4947.852,856.667,4987.611,894.403C5027.37,932.14,5106.888,999.28,5146.647,1032.85L5186.406,1066.419"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F5_VUE_APP_49" d="M5125.391,503L5125.391,534.333C5125.391,565.667,5125.391,628.333,5125.391,663.833C5125.391,699.333,5125.391,707.667,5125.391,716C5125.391,724.333,5125.391,732.667,5125.391,743C5125.391,753.333,5125.391,765.667,5125.391,778C5125.391,790.333,5125.391,802.667,5125.391,813C5125.391,823.333,5125.391,831.667,5125.391,840C5125.391,848.333,5125.391,856.667,5140.543,894.226C5155.695,931.786,5185.999,998.572,5201.151,1031.965L5216.303,1065.357"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F6_VUE_APP_50" d="M5318.93,503L5318.93,534.333C5318.93,565.667,5318.93,628.333,5318.93,663.833C5318.93,699.333,5318.93,707.667,5318.93,716C5318.93,724.333,5318.93,732.667,5318.93,743C5318.93,753.333,5318.93,765.667,5318.93,778C5318.93,790.333,5318.93,802.667,5318.93,813C5318.93,823.333,5318.93,831.667,5318.93,840C5318.93,848.333,5318.93,856.667,5307.494,894.203C5296.058,931.739,5273.186,998.477,5261.75,1031.847L5250.315,1065.216"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TASK_EXT1_51" d="M1599.056,1553L1603.601,1557.167C1608.147,1561.333,1617.237,1569.667,1621.783,1578C1626.328,1586.333,1626.328,1594.667,1626.328,1603C1626.328,1611.333,1626.328,1619.667,1467.561,1628C1308.794,1636.333,991.26,1644.667,832.493,1659.5C673.727,1674.333,673.727,1695.667,673.727,1717C673.727,1738.333,673.727,1759.667,673.727,1774.5C673.727,1789.333,673.727,1797.667,673.727,1806C673.727,1814.333,673.727,1822.667,673.727,1831C673.727,1839.333,673.727,1847.667,673.727,1856C673.727,1864.333,673.727,1872.667,673.727,1881C673.727,1889.333,673.727,1897.667,673.727,1905.333C673.727,1913,673.727,1920,673.727,1923.5L673.727,1927"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TASK_EXT2_52" d="M1539.145,1553L1537.289,1557.167C1535.434,1561.333,1531.723,1569.667,1529.867,1578C1528.012,1586.333,1528.012,1594.667,1528.012,1603C1528.012,1611.333,1528.012,1619.667,1356.192,1628C1184.372,1636.333,840.733,1644.667,668.913,1659.5C497.094,1674.333,497.094,1695.667,497.094,1717C497.094,1738.333,497.094,1759.667,497.094,1774.5C497.094,1789.333,497.094,1797.667,497.094,1806C497.094,1814.333,497.094,1822.667,497.094,1831C497.094,1839.333,497.094,1847.667,497.094,1856C497.094,1864.333,497.094,1872.667,497.094,1881C497.094,1889.333,497.094,1897.667,497.094,1905.333C497.094,1913,497.094,1920,497.094,1923.5L497.094,1927"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_VUE_APP_EXT3_53" d="M5132.576,1147L5042.714,1181C4952.853,1215,4773.129,1283,4683.268,1321.167C4593.406,1359.333,4593.406,1367.667,4593.406,1378C4593.406,1388.333,4593.406,1400.667,4593.406,1413C4593.406,1425.333,4593.406,1437.667,4593.406,1454.5C4593.406,1471.333,4593.406,1492.667,4593.406,1514C4593.406,1535.333,4593.406,1556.667,4593.406,1571.5C4593.406,1586.333,4593.406,1594.667,4593.406,1603C4593.406,1611.333,4593.406,1619.667,4593.406,1628C4593.406,1636.333,4593.406,1644.667,4593.406,1659.5C4593.406,1674.333,4593.406,1695.667,4593.406,1717C4593.406,1738.333,4593.406,1759.667,4593.406,1774.5C4593.406,1789.333,4593.406,1797.667,4593.406,1806C4593.406,1814.333,4593.406,1822.667,4593.406,1831C4593.406,1839.333,4593.406,1847.667,4217.252,1856C3841.098,1864.333,3088.789,1872.667,2712.635,1881C2336.48,1889.333,2336.48,1897.667,2336.48,1905.333C2336.48,1913,2336.48,1920,2336.48,1923.5L2336.48,1927"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API2_EXT4_54" d="M2026.342,1756L2019.936,1760.167C2013.529,1764.333,2000.716,1772.667,1994.309,1781C1987.902,1789.333,1987.902,1797.667,1987.902,1806C1987.902,1814.333,1987.902,1822.667,1987.902,1831C1987.902,1839.333,1987.902,1847.667,1854.254,1856C1720.605,1864.333,1453.309,1872.667,1319.66,1881C1186.012,1889.333,1186.012,1897.667,1186.012,1905.333C1186.012,1913,1186.012,1920,1186.012,1923.5L1186.012,1927"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MONITOR1_API1_55" d="M1376.563,503L1383.741,534.333C1390.918,565.667,1405.274,628.333,1412.451,663.833C1419.629,699.333,1419.629,707.667,1419.629,716C1419.629,724.333,1419.629,732.667,1419.629,743C1419.629,753.333,1419.629,765.667,1419.629,778C1419.629,790.333,1419.629,802.667,1419.629,813C1419.629,823.333,1419.629,831.667,1419.629,840C1419.629,848.333,1419.629,856.667,1419.629,901.333C1419.629,946,1419.629,1027,1419.629,1108C1419.629,1189,1419.629,1270,1419.629,1314.667C1419.629,1359.333,1419.629,1367.667,1419.629,1378C1419.629,1388.333,1419.629,1400.667,1419.629,1413C1419.629,1425.333,1419.629,1437.667,1419.629,1454.5C1419.629,1471.333,1419.629,1492.667,1419.629,1514C1419.629,1535.333,1419.629,1556.667,1419.629,1571.5C1419.629,1586.333,1419.629,1594.667,1419.629,1603C1419.629,1611.333,1419.629,1619.667,1491.248,1628C1562.867,1636.333,1706.105,1644.667,1837.127,1657.598C1968.148,1670.529,2086.953,1688.057,2146.355,1696.821L2205.758,1705.586"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MONITOR1_API2_56" d="M1367.629,503L1367.629,534.333C1367.629,565.667,1367.629,628.333,1367.629,663.833C1367.629,699.333,1367.629,707.667,1367.629,716C1367.629,724.333,1367.629,732.667,1367.629,743C1367.629,753.333,1367.629,765.667,1367.629,778C1367.629,790.333,1367.629,802.667,1367.629,813C1367.629,823.333,1367.629,831.667,1367.629,840C1367.629,848.333,1367.629,856.667,1367.629,901.333C1367.629,946,1367.629,1027,1367.629,1108C1367.629,1189,1367.629,1270,1367.629,1314.667C1367.629,1359.333,1367.629,1367.667,1367.629,1378C1367.629,1388.333,1367.629,1400.667,1367.629,1413C1367.629,1425.333,1367.629,1437.667,1367.629,1454.5C1367.629,1471.333,1367.629,1492.667,1367.629,1514C1367.629,1535.333,1367.629,1556.667,1367.629,1571.5C1367.629,1586.333,1367.629,1594.667,1367.629,1603C1367.629,1611.333,1367.629,1619.667,1427.18,1628C1486.732,1636.333,1605.835,1644.667,1712.724,1657.217C1819.613,1669.767,1914.288,1686.535,1961.626,1694.918L2008.964,1703.302"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MONITOR1_API3_57" d="M1358.695,503L1351.517,534.333C1344.34,565.667,1329.984,628.333,1322.807,663.833C1315.629,699.333,1315.629,707.667,1315.629,716C1315.629,724.333,1315.629,732.667,1315.629,743C1315.629,753.333,1315.629,765.667,1315.629,778C1315.629,790.333,1315.629,802.667,1315.629,813C1315.629,823.333,1315.629,831.667,1315.629,840C1315.629,848.333,1315.629,856.667,1315.629,901.333C1315.629,946,1315.629,1027,1315.629,1108C1315.629,1189,1315.629,1270,1315.629,1314.667C1315.629,1359.333,1315.629,1367.667,1315.629,1378C1315.629,1388.333,1315.629,1400.667,1315.629,1413C1315.629,1425.333,1315.629,1437.667,1315.629,1454.5C1315.629,1471.333,1315.629,1492.667,1315.629,1514C1315.629,1535.333,1315.629,1556.667,1315.629,1571.5C1315.629,1586.333,1315.629,1594.667,1315.629,1603C1315.629,1611.333,1315.629,1619.667,1363.113,1628C1410.596,1636.333,1505.564,1644.667,1588.323,1656.646C1671.082,1668.626,1741.633,1684.251,1776.909,1692.064L1812.184,1699.877"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MONITOR2_WORKER1_58" d="M2759.114,503L2766.291,534.333C2773.469,565.667,2787.824,628.333,2795.002,663.833C2802.18,699.333,2802.18,707.667,2802.18,716C2802.18,724.333,2802.18,732.667,2802.18,743C2802.18,753.333,2802.18,765.667,2802.18,778C2802.18,790.333,2802.18,802.667,2802.18,813C2802.18,823.333,2802.18,831.667,2802.18,840C2802.18,848.333,2802.18,856.667,2802.18,901.333C2802.18,946,2802.18,1027,2802.18,1108C2802.18,1189,2802.18,1270,2802.18,1314.667C2802.18,1359.333,2802.18,1367.667,2802.18,1378C2802.18,1388.333,2802.18,1400.667,2802.18,1413C2802.18,1425.333,2802.18,1437.667,2737.799,1452.679C2673.419,1467.691,2544.657,1485.383,2480.277,1494.229L2415.896,1503.074"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MONITOR2_WORKER2_59" d="M2750.18,503L2750.18,534.333C2750.18,565.667,2750.18,628.333,2750.18,663.833C2750.18,699.333,2750.18,707.667,2750.18,716C2750.18,724.333,2750.18,732.667,2750.18,743C2750.18,753.333,2750.18,765.667,2750.18,778C2750.18,790.333,2750.18,802.667,2750.18,813C2750.18,823.333,2750.18,831.667,2750.18,840C2750.18,848.333,2750.18,856.667,2750.18,901.333C2750.18,946,2750.18,1027,2750.18,1108C2750.18,1189,2750.18,1270,2750.18,1314.667C2750.18,1359.333,2750.18,1367.667,2750.18,1378C2750.18,1388.333,2750.18,1400.667,2750.18,1413C2750.18,1425.333,2750.18,1437.667,2750.18,1447.333C2750.18,1457,2750.18,1464,2750.18,1467.5L2750.18,1471"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MONITOR2_WORKER3_60" d="M2702.269,503L2663.777,534.333C2625.285,565.667,2548.301,628.333,2509.809,663.833C2471.316,699.333,2471.316,707.667,2471.316,716C2471.316,724.333,2471.316,732.667,2471.316,743C2471.316,753.333,2471.316,765.667,2471.316,778C2471.316,790.333,2471.316,802.667,2471.316,813C2471.316,823.333,2471.316,831.667,2471.316,840C2471.316,848.333,2471.316,856.667,2471.316,901.333C2471.316,946,2471.316,1027,2471.316,1108C2471.316,1189,2471.316,1270,2471.316,1314.667C2471.316,1359.333,2471.316,1367.667,2471.316,1378C2471.316,1388.333,2471.316,1400.667,2471.316,1413C2471.316,1425.333,2471.316,1437.667,2411.634,1452.378C2351.951,1467.09,2232.586,1484.181,2172.904,1492.726L2113.221,1501.271"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MONITOR3_MYSQL_M_61" d="M3791.338,503L3798.709,534.333C3806.08,565.667,3820.821,628.333,3828.192,663.833C3835.563,699.333,3835.563,707.667,3835.563,716C3835.563,724.333,3835.563,732.667,3835.563,743C3835.563,753.333,3835.563,765.667,3835.563,778C3835.563,790.333,3835.563,802.667,3835.563,813C3835.563,823.333,3835.563,831.667,3835.563,840C3835.563,848.333,3835.563,856.667,3835.563,901.333C3835.563,946,3835.563,1027,3835.563,1108C3835.563,1189,3835.563,1270,3835.563,1314.667C3835.563,1359.333,3835.563,1367.667,3835.563,1378C3835.563,1388.333,3835.563,1400.667,3835.563,1413C3835.563,1425.333,3835.563,1437.667,3835.563,1454.5C3835.563,1471.333,3835.563,1492.667,3835.563,1514C3835.563,1535.333,3835.563,1556.667,3835.563,1571.5C3835.563,1586.333,3835.563,1594.667,3835.563,1603C3835.563,1611.333,3835.563,1619.667,3835.563,1628C3835.563,1636.333,3835.563,1644.667,3835.563,1659.5C3835.563,1674.333,3835.563,1695.667,3835.563,1717C3835.563,1738.333,3835.563,1759.667,3835.563,1774.5C3835.563,1789.333,3835.563,1797.667,3835.563,1806C3835.563,1814.333,3835.563,1822.667,3835.563,1831C3835.563,1839.333,3835.563,1847.667,4129.09,1856C4422.618,1864.333,5009.674,1872.667,5117.875,1881C5226.076,1889.333,4855.421,1897.667,4666.082,1905.547C4476.744,1913.427,4468.723,1920.855,4464.712,1924.569L4460.702,1928.282"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MONITOR3_REDIS_M_62" d="M3777.697,503L3774.108,534.333C3770.519,565.667,3763.342,628.333,3759.753,663.833C3756.164,699.333,3756.164,707.667,3756.164,716C3756.164,724.333,3756.164,732.667,3756.164,743C3756.164,753.333,3756.164,765.667,3756.164,778C3756.164,790.333,3756.164,802.667,3756.164,813C3756.164,823.333,3756.164,831.667,3756.164,840C3756.164,848.333,3756.164,856.667,3756.164,901.333C3756.164,946,3756.164,1027,3756.164,1108C3756.164,1189,3756.164,1270,3756.164,1314.667C3756.164,1359.333,3756.164,1367.667,3756.164,1378C3756.164,1388.333,3756.164,1400.667,3756.164,1413C3756.164,1425.333,3756.164,1437.667,3756.164,1454.5C3756.164,1471.333,3756.164,1492.667,3756.164,1514C3756.164,1535.333,3756.164,1556.667,3756.164,1571.5C3756.164,1586.333,3756.164,1594.667,3756.164,1603C3756.164,1611.333,3756.164,1619.667,3756.164,1628C3756.164,1636.333,3756.164,1644.667,3756.164,1659.5C3756.164,1674.333,3756.164,1695.667,3756.164,1717C3756.164,1738.333,3756.164,1759.667,3756.164,1774.5C3756.164,1789.333,3756.164,1797.667,3756.164,1806C3756.164,1814.333,3756.164,1822.667,3756.164,1831C3756.164,1839.333,3756.164,1847.667,4044.381,1856C4332.598,1864.333,4909.031,1872.667,5251.129,1881C5593.227,1889.333,5700.988,1897.667,5710.016,1910.01C5719.043,1922.354,5629.337,1938.708,5584.484,1946.885L5539.63,1955.063"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LOG1_API1_63" d="M693.5,503L706.471,534.333C719.441,565.667,745.383,628.333,758.353,663.833C771.324,699.333,771.324,707.667,771.324,716C771.324,724.333,771.324,732.667,771.324,743C771.324,753.333,771.324,765.667,771.324,778C771.324,790.333,771.324,802.667,807.845,813C844.366,823.333,917.408,831.667,953.928,840C990.449,848.333,990.449,856.667,990.449,901.333C990.449,946,990.449,1027,990.449,1108C990.449,1189,990.449,1270,990.449,1314.667C990.449,1359.333,990.449,1367.667,990.449,1378C990.449,1388.333,990.449,1400.667,990.449,1413C990.449,1425.333,990.449,1437.667,990.449,1454.5C990.449,1471.333,990.449,1492.667,990.449,1514C990.449,1535.333,990.449,1556.667,990.449,1571.5C990.449,1586.333,990.449,1594.667,990.449,1603C990.449,1611.333,990.449,1619.667,1060.157,1628C1129.865,1636.333,1269.28,1644.667,1471.826,1658.556C1674.372,1672.445,1940.049,1691.89,2072.887,1701.613L2205.726,1711.335"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LOG1_API2_64" d="M687.315,503L695.316,534.333C703.318,565.667,719.321,628.333,727.323,663.833C735.324,699.333,735.324,707.667,735.324,716C735.324,724.333,735.324,732.667,735.324,743C735.324,753.333,735.324,765.667,735.324,778C735.324,790.333,735.324,802.667,769.178,813C803.033,823.333,870.741,831.667,904.595,840C938.449,848.333,938.449,856.667,938.449,901.333C938.449,946,938.449,1027,938.449,1108C938.449,1189,938.449,1270,938.449,1314.667C938.449,1359.333,938.449,1367.667,938.449,1378C938.449,1388.333,938.449,1400.667,938.449,1413C938.449,1425.333,938.449,1437.667,938.449,1454.5C938.449,1471.333,938.449,1492.667,938.449,1514C938.449,1535.333,938.449,1556.667,938.449,1571.5C938.449,1586.333,938.449,1594.667,938.449,1603C938.449,1611.333,938.449,1619.667,1013.49,1628C1088.531,1636.333,1238.613,1644.667,1417.025,1658.317C1595.437,1671.967,1802.178,1690.933,1905.548,1700.417L2008.919,1709.9"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LOG1_API3_65" d="M668.422,503L661.244,534.333C654.066,565.667,639.711,628.333,632.533,663.833C625.355,699.333,625.355,707.667,625.355,716C625.355,724.333,625.355,732.667,625.355,743C625.355,753.333,625.355,765.667,625.355,778C625.355,790.333,625.355,802.667,668.871,813C712.387,823.333,799.418,831.667,842.934,840C886.449,848.333,886.449,856.667,886.449,901.333C886.449,946,886.449,1027,886.449,1108C886.449,1189,886.449,1270,886.449,1314.667C886.449,1359.333,886.449,1367.667,886.449,1378C886.449,1388.333,886.449,1400.667,886.449,1413C886.449,1425.333,886.449,1437.667,886.449,1454.5C886.449,1471.333,886.449,1492.667,886.449,1514C886.449,1535.333,886.449,1556.667,886.449,1571.5C886.449,1586.333,886.449,1594.667,886.449,1603C886.449,1611.333,886.449,1619.667,966.824,1628C1047.198,1636.333,1207.947,1644.667,1362.225,1657.915C1516.503,1671.164,1664.312,1689.328,1738.216,1698.409L1812.12,1707.491"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LOG2_NGINX_66" d="M885.293,503L885.293,534.333C885.293,565.667,885.293,628.333,885.293,663.833C885.293,699.333,885.293,707.667,885.293,716C885.293,724.333,885.293,732.667,885.293,743C885.293,753.333,885.293,765.667,885.293,778C885.293,790.333,885.293,802.667,764.406,813C643.518,823.333,401.743,831.667,280.856,840C159.969,848.333,159.969,856.667,159.969,901.333C159.969,946,159.969,1027,159.969,1108C159.969,1189,159.969,1270,159.969,1314.667C159.969,1359.333,159.969,1367.667,159.969,1378C159.969,1388.333,159.969,1400.667,714.227,1413C1268.486,1425.333,2377.003,1437.667,2934.504,1447.501C3492.006,1457.335,3498.492,1464.669,3501.735,1468.336L3504.978,1472.004"></path><path marker-end="url(#mermaid-c90fb5f4-4b08-4382-8851-4f9972fa7855_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LOG3_EMQX_67" d="M1098.895,503L1098.895,534.333C1098.895,565.667,1098.895,628.333,1098.895,663.833C1098.895,699.333,1098.895,707.667,1098.895,716C1098.895,724.333,1098.895,732.667,1098.895,743C1098.895,753.333,1098.895,765.667,1999.947,778C2901,790.333,4703.105,802.667,5604.158,813C6505.211,823.333,6505.211,831.667,6505.211,840C6505.211,848.333,6505.211,856.667,6562.806,894.497C6620.4,932.327,6735.59,999.654,6793.184,1033.318L6850.779,1066.982"></path></g><g class="edgeLabels"><g transform="translate(6599.37109375, 778)" class="edgeLabel"><g transform="translate(-36.375, -12)" class="label"><foreignObject height="24" width="72.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>MQTT发布</p></span></div></foreignObject></g></g><g transform="translate(6813.76171875, 778)" class="edgeLabel"><g transform="translate(-36.375, -12)" class="label"><foreignObject height="24" width="72.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>MQTT发布</p></span></div></foreignObject></g></g><g transform="translate(7028.15234375, 778)" class="edgeLabel"><g transform="translate(-36.375, -12)" class="label"><foreignObject height="24" width="72.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>MQTT发布</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(2858.5546875, 1413)" class="edgeLabel"><g transform="translate(-36.375, -12)" class="label"><foreignObject height="24" width="72.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>MQTT订阅</p></span></div></foreignObject></g></g><g transform="translate(3242.8515625, 1514)" class="edgeLabel"><g transform="translate(-38.9453125, -12)" class="label"><foreignObject height="24" width="77.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>WebSocket</p></span></div></foreignObject></g></g><g transform="translate(3071.765625, 1514)" class="edgeLabel"><g transform="translate(-38.9453125, -12)" class="label"><foreignObject height="24" width="77.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>WebSocket</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(3654.1484375, 1413)" class="edgeLabel"><g transform="translate(-46.015625, -12)" class="label"><foreignObject height="24" width="92.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP/HTTPS</p></span></div></foreignObject></g></g><g transform="translate(2973.875, 1514)" class="edgeLabel"><g transform="translate(-38.9453125, -12)" class="label"><foreignObject height="24" width="77.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>WebSocket</p></span></div></foreignObject></g></g><g transform="translate(3542.1171875, 1413)" class="edgeLabel"><g transform="translate(-46.015625, -12)" class="label"><foreignObject height="24" width="92.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP/HTTPS</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1419.62890625, 1413)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>监控</p></span></div></foreignObject></g></g><g transform="translate(1367.62890625, 1413)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>监控</p></span></div></foreignObject></g></g><g transform="translate(1315.62890625, 1413)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>监控</p></span></div></foreignObject></g></g><g transform="translate(2802.1796875, 1108)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>监控</p></span></div></foreignObject></g></g><g transform="translate(2750.1796875, 1108)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>监控</p></span></div></foreignObject></g></g><g transform="translate(2471.31640625, 1108)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>监控</p></span></div></foreignObject></g></g><g transform="translate(3835.5625, 1514)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>监控</p></span></div></foreignObject></g></g><g transform="translate(3756.1640625, 1514)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>监控</p></span></div></foreignObject></g></g><g transform="translate(990.44921875, 1413)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>收集</p></span></div></foreignObject></g></g><g transform="translate(938.44921875, 1413)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>收集</p></span></div></foreignObject></g></g><g transform="translate(886.44921875, 1413)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>收集</p></span></div></foreignObject></g></g><g transform="translate(159.96875, 1108)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>收集</p></span></div></foreignObject></g></g><g transform="translate(1098.89453125, 778)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>收集</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(277.35546875, 254)" class="root"><g class="clusters"><g data-look="classic" id="部署环境" class="cluster"><rect height="404" width="229.421875" y="8" x="8" style=""></rect><g transform="translate(90.7109375, 8)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>部署环境</p></span></div></foreignObject></g></g></g><g class="edgePaths"></g><g class="edgeLabels"></g><g class="nodes"><g transform="translate(122.7109375, 82)" id="flowchart-ENV1-1162" class="node default devopsClass"><rect height="78" width="154.421875" y="-39" x="-77.2109375" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-47.2109375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="94.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>开发环境<br>Development</p></span></div></foreignObject></g></g><g transform="translate(122.7109375, 210)" id="flowchart-ENV2-1163" class="node default devopsClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>测试环境<br>Testing</p></span></div></foreignObject></g></g><g transform="translate(122.7109375, 338)" id="flowchart-ENV3-1164" class="node default devopsClass"><rect height="78" width="136.796875" y="-39" x="-68.3984375" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-38.3984375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="76.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>生产环境<br>Production</p></span></div></foreignObject></g></g></g></g><g transform="translate(6492.2109375, 882)" class="root"><g class="clusters"><g data-look="classic" id="通信协议" class="cluster"><rect height="436" width="239.40625" y="8" x="8" style=""></rect><g transform="translate(95.703125, 8)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>通信协议</p></span></div></foreignObject></g></g></g><g class="edgePaths"></g><g class="edgeLabels"></g><g class="nodes"><g transform="translate(127.703125, 70)" id="flowchart-P1-1121" class="node default commClass"><rect height="54" width="164.40625" y="-27" x="-82.203125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-52.203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="104.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MQTT Protocol</p></span></div></foreignObject></g></g><g transform="translate(127.703125, 174)" id="flowchart-P2-1122" class="node default commClass"><rect height="54" width="109.546875" y="-27" x="-54.7734375" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-24.7734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="49.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TCP/IP</p></span></div></foreignObject></g></g><g transform="translate(127.703125, 278)" id="flowchart-P3-1123" class="node default commClass"><rect height="54" width="137.890625" y="-27" x="-68.9453125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-38.9453125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="77.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WebSocket</p></span></div></foreignObject></g></g><g transform="translate(127.703125, 382)" id="flowchart-P4-1124" class="node default commClass"><rect height="54" width="152.03125" y="-27" x="-76.015625" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-46.015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="92.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>HTTP/HTTPS</p></span></div></foreignObject></g></g></g></g><g transform="translate(6599.37109375, 464)" id="flowchart-OW1-1112" class="node default deviceClass"><rect height="78" width="164.390625" y="-39" x="-82.1953125" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-52.1953125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="104.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>油井设备 1<br>传感器/执行器</p></span></div></foreignObject></g></g><g transform="translate(6813.76171875, 464)" id="flowchart-OW2-1113" class="node default deviceClass"><rect height="78" width="164.390625" y="-39" x="-82.1953125" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-52.1953125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="104.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>油井设备 2<br>传感器/执行器</p></span></div></foreignObject></g></g><g transform="translate(7028.15234375, 464)" id="flowchart-OW3-1114" class="node default deviceClass"><rect height="78" width="164.390625" y="-39" x="-82.1953125" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-52.1953125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="104.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>油井设备 N<br>传感器/执行器</p></span></div></foreignObject></g></g><g transform="translate(6089.125, 85)" id="flowchart-D1-1115" class="node default deviceClass"><rect height="54" width="132.390625" y="-27" x="-66.1953125" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-36.1953125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="72.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>内压/外压</p></span></div></foreignObject></g></g><g transform="translate(6267.3203125, 85)" id="flowchart-D2-1116" class="node default deviceClass"><rect height="54" width="124" y="-27" x="-62" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>流量数据</p></span></div></foreignObject></g></g><g transform="translate(6441.3203125, 85)" id="flowchart-D3-1117" class="node default deviceClass"><rect height="54" width="124" y="-27" x="-62" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>温度数据</p></span></div></foreignObject></g></g><g transform="translate(6615.3203125, 85)" id="flowchart-D4-1118" class="node default deviceClass"><rect height="54" width="124" y="-27" x="-62" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>阀门开度</p></span></div></foreignObject></g></g><g transform="translate(6789.3203125, 85)" id="flowchart-D5-1119" class="node default deviceClass"><rect height="54" width="124" y="-27" x="-62" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设备状态</p></span></div></foreignObject></g></g><g transform="translate(6920.95703125, 1108)" id="flowchart-EMQX-1120" class="node default commClass"><rect height="78" width="196.609375" y="-39" x="-98.3046875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-68.3046875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="136.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>EMQX MQTT Broker<br>消息代理服务器</p></span></div></foreignObject></g></g><g transform="translate(2283.12109375, 1717)" id="flowchart-API1-1125" class="node default backendClass"><rect height="78" width="146.8125" y="-39" x="-73.40625" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-43.40625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="86.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API服务器1<br>设备管理API</p></span></div></foreignObject></g></g><g transform="translate(2086.30859375, 1717)" id="flowchart-API2-1126" class="node default backendClass"><rect height="78" width="146.8125" y="-39" x="-73.40625" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-43.40625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="86.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API服务器2<br>数据采集API</p></span></div></foreignObject></g></g><g transform="translate(1889.49609375, 1717)" id="flowchart-API3-1127" class="node default backendClass"><rect height="78" width="146.8125" y="-39" x="-73.40625" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-43.40625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="86.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API服务器3<br>历史数据API</p></span></div></foreignObject></g></g><g transform="translate(3011.91796875, 1717)" id="flowchart-WS1-1128" class="node default backendClass"><rect height="78" width="194.28125" y="-39" x="-97.140625" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-67.140625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="134.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WebSocket服务器1<br>实时数据推送</p></span></div></foreignObject></g></g><g transform="translate(2767.63671875, 1717)" id="flowchart-WS2-1129" class="node default backendClass"><rect height="78" width="194.28125" y="-39" x="-97.140625" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-67.140625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="134.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WebSocket服务器2<br>实时监控</p></span></div></foreignObject></g></g><g transform="translate(1861.796875, 1108)" id="flowchart-MASTER-1130" class="node default backendClass"><rect height="78" width="139.53125" y="-39" x="-69.765625" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-39.765625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="79.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Master进程<br>进程管理</p></span></div></foreignObject></g></g><g transform="translate(2336.37890625, 1514)" id="flowchart-WORKER1-1131" class="node default backendClass"><rect height="78" width="151.109375" y="-39" x="-75.5546875" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-45.5546875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="91.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Worker进程1<br>HTTP处理</p></span></div></foreignObject></g></g><g transform="translate(2750.1796875, 1514)" id="flowchart-WORKER2-1132" class="node default backendClass"><rect height="78" width="151.109375" y="-39" x="-75.5546875" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-45.5546875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="91.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Worker进程2<br>MQTT处理</p></span></div></foreignObject></g></g><g transform="translate(2024.31640625, 1514)" id="flowchart-WORKER3-1133" class="node default backendClass"><rect height="78" width="169.890625" y="-39" x="-84.9453125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-54.9453125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="109.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Worker进程3<br>WebSocket处理</p></span></div></foreignObject></g></g><g transform="translate(1556.51171875, 1514)" id="flowchart-TASK-1134" class="node default backendClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Task进程<br>异步任务</p></span></div></foreignObject></g></g><g transform="translate(3542.1171875, 1514)" id="flowchart-NGINX-1135" class="node default backendClass"><rect height="78" width="196.390625" y="-39" x="-98.1953125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-68.1953125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="136.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Nginx<br>反向代理/负载均衡</p></span></div></foreignObject></g></g><g transform="translate(4415.6484375, 1970)" id="flowchart-MYSQL_M-1136" class="node default dataClass"><rect height="78" width="196.390625" y="-39" x="-98.1953125" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-68.1953125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="136.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MySQL 8 主库<br>设备信息/用户数据</p></span></div></foreignObject></g></g><g transform="translate(4349.05078125, 2148)" id="flowchart-MYSQL_S1-1137" class="node default dataClass"><rect height="78" width="163.6875" y="-39" x="-81.84375" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-51.84375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="103.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MySQL 8 从库1<br>读取负载分担</p></span></div></foreignObject></g></g><g transform="translate(4135.36328125, 2148)" id="flowchart-MYSQL_S2-1138" class="node default dataClass"><rect height="78" width="163.6875" y="-39" x="-81.84375" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-51.84375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="103.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MySQL 8 从库2<br>读取负载分担</p></span></div></foreignObject></g></g><g transform="translate(5457.6953125, 1970)" id="flowchart-REDIS_M-1139" class="node default dataClass"><rect height="78" width="156" y="-39" x="-78" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis 主节点<br>实时数据缓存</p></span></div></foreignObject></g></g><g transform="translate(5401.8359375, 2148)" id="flowchart-REDIS_S1-1140" class="node default dataClass"><rect height="78" width="158.5625" y="-39" x="-79.28125" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-49.28125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="98.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis 从节点1<br>会话存储</p></span></div></foreignObject></g></g><g transform="translate(4887.109375, 2148)" id="flowchart-REDIS_S2-1141" class="node default dataClass"><rect height="78" width="158.5625" y="-39" x="-79.28125" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-49.28125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="98.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis 从节点2<br>队列缓存</p></span></div></foreignObject></g></g><g transform="translate(3491.44921875, 1970)" id="flowchart-ES1-1142" class="node default dataClass"><rect height="78" width="198.703125" y="-39" x="-99.3515625" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-69.3515625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="138.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Elasticsearch 节点1<br>历史数据索引</p></span></div></foreignObject></g></g><g transform="translate(3242.74609375, 1970)" id="flowchart-ES2-1143" class="node default dataClass"><rect height="78" width="198.703125" y="-39" x="-99.3515625" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-69.3515625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="138.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Elasticsearch 节点2<br>日志数据存储</p></span></div></foreignObject></g></g><g transform="translate(2994.04296875, 1970)" id="flowchart-ES3-1144" class="node default dataClass"><rect height="78" width="198.703125" y="-39" x="-99.3515625" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-69.3515625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="138.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Elasticsearch 节点3<br>全文搜索</p></span></div></foreignObject></g></g><g transform="translate(3807.14453125, 1970)" id="flowchart-FS-1145" class="node default dataClass"><rect height="78" width="172.78125" y="-39" x="-86.390625" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-56.390625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="112.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>文件系统<br>图片/日志/报告</p></span></div></foreignObject></g></g><g transform="translate(5235.65234375, 1108)" id="flowchart-VUE_APP-1146" class="node default frontendClass"><rect height="78" width="259.265625" y="-39" x="-129.6328125" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-99.6328125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="199.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Vue 3 + Vite<br>helioCloud-devops-frontend</p></span></div></foreignObject></g></g><g transform="translate(5794.9296875, 464)" id="flowchart-PINIA-1147" class="node default frontendClass"><rect height="78" width="156" y="-39" x="-78" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Pinia Store<br>全局状态管理</p></span></div></foreignObject></g></g><g transform="translate(5556.9296875, 464)" id="flowchart-SASS-1148" class="node default frontendClass"><rect height="78" width="140" y="-39" x="-70" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-40, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Sass/SCSS<br>样式预处理</p></span></div></foreignObject></g></g><g transform="translate(4331.9609375, 464)" id="flowchart-F1-1149" class="node default frontendClass"><rect height="78" width="156" y="-39" x="-78" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设备监控面板<br>ECharts图表</p></span></div></foreignObject></g></g><g transform="translate(4537.9609375, 464)" id="flowchart-F2-1150" class="node default frontendClass"><rect height="78" width="156" y="-39" x="-78" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>历史数据分析<br>数据查询</p></span></div></foreignObject></g></g><g transform="translate(4750.90625, 464)" id="flowchart-F3-1151" class="node default frontendClass"><rect height="78" width="169.890625" y="-39" x="-84.9453125" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-54.9453125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="109.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>实时图表展示<br>WebSocket连接</p></span></div></foreignObject></g></g><g transform="translate(4947.8515625, 464)" id="flowchart-F4-1152" class="node default frontendClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>告警管理<br>规则配置</p></span></div></foreignObject></g></g><g transform="translate(5125.390625, 464)" id="flowchart-F5-1153" class="node default frontendClass"><rect height="78" width="131.078125" y="-39" x="-65.5390625" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-35.5390625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="71.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设备管理<br>CRUD操作</p></span></div></foreignObject></g></g><g transform="translate(5318.9296875, 464)" id="flowchart-F6-1154" class="node default frontendClass"><rect height="78" width="156" y="-39" x="-78" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户权限管理<br>角色控制</p></span></div></foreignObject></g></g><g transform="translate(4073.9609375, 1108)" id="flowchart-MOBILE-1155" class="node default frontendClass"><rect height="78" width="140" y="-39" x="-70" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-40, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>移动端应用<br>H5/小程序</p></span></div></foreignObject></g></g><g transform="translate(1367.62890625, 464)" id="flowchart-MONITOR1-1156" class="node default devopsClass"><rect height="78" width="169.65625" y="-39" x="-84.828125" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-54.828125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="109.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>系统监控<br>CPU/内存/磁盘</p></span></div></foreignObject></g></g><g transform="translate(2750.1796875, 464)" id="flowchart-MONITOR2-1157" class="node default devopsClass"><rect height="78" width="181.46875" y="-39" x="-90.734375" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-60.734375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="121.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>应用监控<br>PHP-FPM/Swoole</p></span></div></foreignObject></g></g><g transform="translate(3782.1640625, 464)" id="flowchart-MONITOR3-1158" class="node default devopsClass"><rect height="78" width="183.59375" y="-39" x="-91.796875" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-61.796875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="123.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>数据库监控<br>MySQL/Redis性能</p></span></div></foreignObject></g></g><g transform="translate(677.35546875, 464)" id="flowchart-LOG1-1159" class="node default devopsClass"><rect height="78" width="152.3125" y="-39" x="-76.15625" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-46.15625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="92.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>应用日志<br>PHP错误日志</p></span></div></foreignObject></g></g><g transform="translate(885.29296875, 464)" id="flowchart-LOG2-1160" class="node default devopsClass"><rect height="78" width="163.5625" y="-39" x="-81.78125" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-51.78125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="103.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>访问日志<br>Nginx访问日志</p></span></div></foreignObject></g></g><g transform="translate(1098.89453125, 464)" id="flowchart-LOG3-1161" class="node default devopsClass"><rect height="78" width="163.640625" y="-39" x="-81.8203125" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-51.8203125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="103.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MQTT日志<br>EMQX连接日志</p></span></div></foreignObject></g></g><g transform="translate(673.7265625, 1970)" id="flowchart-EXT1-1165" class="node default externalClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>短信服务<br>告警通知</p></span></div></foreignObject></g></g><g transform="translate(497.09375, 1970)" id="flowchart-EXT2-1166" class="node default externalClass"><rect height="78" width="129.265625" y="-39" x="-64.6328125" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-34.6328125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="69.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>邮件服务<br>SMTP推送</p></span></div></foreignObject></g></g><g transform="translate(2336.48046875, 1970)" id="flowchart-EXT3-1167" class="node default externalClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>地图服务<br>设备定位</p></span></div></foreignObject></g></g><g transform="translate(1186.01171875, 1970)" id="flowchart-EXT4-1168" class="node default externalClass"><rect height="78" width="124" y="-39" x="-62" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>天气API<br>环境数据</p></span></div></foreignObject></g></g></g></g></g></svg>