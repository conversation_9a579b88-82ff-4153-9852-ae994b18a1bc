const DEFAULT_CONFIG = {
    // 标题
    APP_NAME: import.meta.env.VITE_APP_TITLE,

    // 首页地址
    DASHBOARD_URL: '/dashboard',

    // 版本号
    APP_VER: '1.6.9',

    // 内核版本号
    CORE_VER: '1.6.9',

    // 接口地址
    API_URL: import.meta.env.VITE_APP_API_BASEURL,

    // 请求超时
    TIMEOUT: 60 * 1000,

    // TokenName
    TOKEN_NAME: 'Authorization',

    // Token前缀，注意最后有个空格，如不需要需设置空字符串
    TOKEN_PREFIX: 'Bearer ',

    // 追加其他头
    HEADERS: {
        // 用于区分客户端的唯一标识（告诉服务端来自哪个端的请求，多端部署时需要设置不同）
        'X-Client-Id': 'heli:df'
    },

    // 请求是否开启缓存
    REQUEST_CACHE: false,

    // 布局 默认：default | 通栏：header | 经典：menu | 功能坞：dock
    // dock将关闭标签和面包屑栏
    APP_LAYOUT: 'default',

    // 菜单是否折叠
    MENU_COLLAPSE: false,

    // 菜单是否启用手风琴效果
    MENU_UNIQUE_OPENED: false,

    // 是否开启多标签
    SHOW_TABS: true,

    // 最大标签页数量，0表示不限制
    MAX_TABS: 0,

    // 显示顶部API文档链接
    SHOW_API_DOCS: true,

    // 显示顶部mqtt测试按钮
    SHOW_MQTT_TOOLS_BTN: true,

    // 语言
    APP_LANG: 'zh-cn',

    // 默认主题颜色
    COLOR: '#4d3777',

    // 预设主题色
    COLOR_LIST: ['#4d3777', '#0f2549', '#00985b', '#1C409A', '#554540', '#534055', '#405455'],

    // 默认主题模式 'auto' | 'light' | 'dark'
    DEFAULT_THEME_MODE: 'auto',

    // 是否加密localStorage, 为空不加密，可填写AES(模式ECB,移位Pkcs7)加密
    LS_ENCRYPTION: '',

    // localStorageAES加密秘钥，位数建议填写8的倍数
    LS_ENCRYPTION_key: '2XNN4K8LC0ELVWN4',

    // 控制台首页默认布局
    DEFAULT_GRID: {
        // 默认分栏数量和宽度 例如 [24] [18,6] [8,8,8] [6,12,6]
        layout: [8, 8, 8],
        // 小组件分布，com取值:views/home/<USER>
        copmsList: [
            ['time', 'about'],
            ['sentences', 'ver'],
            ['welcome', 'progress']
        ]
    },

    // 请求追踪回传开关（默认关闭，非特殊需求，服务端每次请求都会生成新的requestId，不需要将每次的请求ID回传过去）
    REQUEST_BACK_TRACKER: false
};

//合并业务配置
import MY_CONFIG from './myConfig';

Object.assign(DEFAULT_CONFIG, MY_CONFIG);

// 如果生产模式，就合并动态的APP_CONFIG
// public/config.js
if (import.meta.env.NODE_ENV === 'production') {
    Object.assign(DEFAULT_CONFIG, APP_CONFIG);
}

export default DEFAULT_CONFIG;
