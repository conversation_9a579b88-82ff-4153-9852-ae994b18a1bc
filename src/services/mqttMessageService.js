import mqttMessageModel from '@/model/mqttMessageModel';
import loggerFactory from '@/utils/logger';
import mqttConfig from '@/config/mqtt';

// 创建自定义前缀的日志记录器
const logger = loggerFactory.createLogger('MQTTMsgService');

/**
 * MQTT消息服务
 * 负责管理MQTT消息的存储和检索
 * 全局持久化MQTT消息，无论mqtt-test组件是否打开
 */
class MqttMessageService {
    constructor() {
        this.initialized = false;
        this.messageCache = {
            received: [],
            sent: []
        };

        // 检查是否启用消息持久化
        if (mqttConfig.messagePersistence.enabled) {
            // 立即初始化，确保全局消息捕获
            this.init().then(() => {
                // 绑定消息处理事件
                window.addEventListener('mqtt-message-received', this.handleMessageReceived.bind(this));
                window.addEventListener('mqtt-message-sent', this.handleMessageSent.bind(this));
            });
        } else {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('MQTT消息持久化已禁用');
            }
        }
    }

    /**
     * 初始化服务
     * 确保数据库初始化并预加载消息
     */
    async init() {
        if (this.initialized) return;

        // 检查是否启用消息持久化
        if (!mqttConfig.messagePersistence.enabled) {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('MQTT消息持久化已禁用，跳过初始化');
            }
            return;
        }

        if (mqttConfig.messagePersistence.enableLogging) {
            logger.group('初始化MQTT消息服务');
            logger.info('开始初始化MQTT消息服务');
        }

        try {
            // 数据库已经在模型构造函数中初始化，这里只需等待初始化完成
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('等待数据库初始化完成');
            }
            await mqttMessageModel.initPromise;
            this.initialized = true;

            // 预加载消息到缓存
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('开始预加载消息到缓存');
            }
            await this.preloadMessages();

            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('MQTT消息服务初始化成功，消息持久化已启用');
                logger.groupEnd();
            }
        } catch (error) {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.error('MQTT消息服务初始化失败:', error);
                logger.groupEnd();
            }
        }
    }

    /**
     * 预加载消息到缓存
     * 确保即使在组件未加载时也能访问历史消息
     */
    async preloadMessages() {
        if (mqttConfig.messagePersistence.enableLogging) {
            logger.group('预加载MQTT消息');
        }

        try {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('加载接收消息');
            }
            this.messageCache.received = await mqttMessageModel.getReceivedMessages();
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info(`已加载 ${this.messageCache.received.length} 条接收消息`);
            }

            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('加载发送消息');
            }
            this.messageCache.sent = await mqttMessageModel.getSentMessages();
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info(`已加载 ${this.messageCache.sent.length} 条发送消息`);
            }

            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('预加载MQTT消息完成');
                logger.groupEnd();
            }
        } catch (error) {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.error('预加载MQTT消息失败:', error);
                logger.groupEnd();
            }
        }
    }

    /**
     * 处理接收到的消息
     * @param {CustomEvent} event
     */
    async handleMessageReceived(event) {
        // 检查是否启用消息持久化
        if (!mqttConfig.messagePersistence.enabled) {
            return;
        }

        if (!this.initialized) {
            // 如果服务尚未初始化，先初始化
            await this.init();
        }

        try {
            const message = event.detail;
            // 保存到数据库
            await mqttMessageModel.addReceivedMessage(message);
            // 更新缓存
            this.messageCache.received = await mqttMessageModel.getReceivedMessages();

            // 使用debug级别记录日志，避免日志过多
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.debug(`已保存接收消息: ${message.topic}`);
            }
        } catch (error) {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.error('保存接收消息失败:', error);
            }
        }
    }

    /**
     * 处理发送的消息
     * @param {CustomEvent} event
     */
    async handleMessageSent(event) {
        // 检查是否启用消息持久化
        if (!mqttConfig.messagePersistence.enabled) {
            return;
        }

        if (!this.initialized) {
            // 如果服务尚未初始化，先初始化
            await this.init();
        }

        try {
            const message = event.detail;
            // 保存到数据库
            await mqttMessageModel.addSentMessage(message);
            // 更新缓存
            this.messageCache.sent = await mqttMessageModel.getSentMessages();

            // 使用debug级别记录日志，避免日志过多
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.debug(`已保存发送消息: ${message.topic}`);
            }
        } catch (error) {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.error('保存发送消息失败:', error);
            }
        }
    }

    /**
     * 获取接收的消息列表
     * @returns {Promise<Array>}
     */
    async getReceivedMessages() {
        // 如果未启用消息持久化，返回空数组
        if (!mqttConfig.messagePersistence.enabled) {
            return [];
        }

        if (!this.initialized) await this.init();

        // 刷新缓存并返回最新数据
        if (mqttConfig.messagePersistence.enableLogging) {
            logger.debug('获取接收消息列表');
        }
        this.messageCache.received = await mqttMessageModel.getReceivedMessages();
        if (mqttConfig.messagePersistence.enableLogging) {
            logger.debug(`获取到 ${this.messageCache.received.length} 条接收消息`);
        }

        return this.messageCache.received;
    }

    /**
     * 获取发送的消息列表
     * @returns {Promise<Array>}
     */
    async getSentMessages() {
        // 如果未启用消息持久化，返回空数组
        if (!mqttConfig.messagePersistence.enabled) {
            return [];
        }

        if (!this.initialized) await this.init();

        // 刷新缓存并返回最新数据
        if (mqttConfig.messagePersistence.enableLogging) {
            logger.debug('获取发送消息列表');
        }
        this.messageCache.sent = await mqttMessageModel.getSentMessages();
        if (mqttConfig.messagePersistence.enableLogging) {
            logger.debug(`获取到 ${this.messageCache.sent.length} 条发送消息`);
        }

        return this.messageCache.sent;
    }

    /**
     * 清理服务
     */
    destroy() {
        if (mqttConfig.messagePersistence.enableLogging) {
            logger.group('销毁MQTT消息服务');
            logger.info('移除事件监听器');
        }

        window.removeEventListener('mqtt-message-received', this.handleMessageReceived);
        window.removeEventListener('mqtt-message-sent', this.handleMessageSent);

        // 关闭数据库连接
        if (this.initialized && mqttMessageModel.close) {
            if (mqttConfig.messagePersistence.enableLogging) {
                logger.info('关闭数据库连接');
            }
            mqttMessageModel.close();
            this.initialized = false;
        }

        if (mqttConfig.messagePersistence.enableLogging) {
            logger.info('MQTT消息服务已销毁');
            logger.groupEnd();
        }
    }
}

const mqttMessageService = new MqttMessageService();
export default mqttMessageService;
