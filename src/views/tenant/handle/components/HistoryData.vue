<template>
    <div class="history-data-container">
        <!-- 查询条件 -->
        <el-card class="query-card" shadow="never">
            <template #header>
                <div class="card-header">
                    <span><b>历史数据展示</b> {{ currentDeviceDisplay }}</span>
                    <div class="header-actions">
                        <el-button-group>
                            <el-button type="primary" @click="queryData" :loading="queryLoading">
                                <el-icon>
                                    <Search />
                                </el-icon>
                                查询
                            </el-button>
                            <el-button @click="resetQuery">
                                <el-icon>
                                    <Refresh />
                                </el-icon>
                                重置
                            </el-button>
                            <el-button type="success" @click="exportData" :disabled="!hasData" :loading="exportLoading">
                                <el-icon>
                                    <Download />
                                </el-icon>
                                导出数据
                            </el-button>
                        </el-button-group>
                    </div>
                </div>
            </template>

            <el-form :model="queryForm" :rules="queryRules" ref="queryFormRef" label-width="100px" class="query-form">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="时间范围" prop="timeRange">
                            <div class="time-range-container">
                                <el-date-picker
                                    v-model="queryForm.timeRange"
                                    type="datetimerange"
                                    range-separator="至"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                    :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                                    :shortcuts="timeShortcuts"
                                    style="width: 100%"
                                />
                            </div>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="分层选择" prop="layer">
                            <el-select
                                v-model="queryForm.layer"
                                placeholder="请选择分层"
                                :disabled="!currentDeviceInfo || !currentDeviceInfo.level"
                                style="width: 100%"
                            >
                                <el-option label="全部分层" :value="null" />
                                <el-option
                                    v-for="layer in layerOptions"
                                    :key="layer"
                                    :label="`第${chineseNumber(layer)}层`"
                                    :value="layer"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="数据间隔">
                            <el-select v-model="queryForm.interval" placeholder="请选择数据间隔" style="width: 100%">
                                <el-option label="1分钟" value="1m" />
                                <el-option label="5分钟" value="5m" />
                                <el-option label="10分钟" value="10m" />
                                <el-option label="30分钟" value="30m" />
                                <el-option label="1小时" value="1h" />
                                <el-option label="6小时" value="6h" />
                                <el-option label="1天" value="1d" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-card>

        <!-- 数据统计 -->
        <el-card class="stats-card" shadow="never" v-if="hasData">
            <template #header>
                <b>数据统计</b>
            </template>
            <div class="stats-row">
                <div class="stat-item-wrapper" v-for="stat in dataStats" :key="stat.type">
                    <div class="stat-item" :style="{ backgroundColor: `${stat.color}0D`, borderColor: `${stat.color}19` }">
                        <div class="stat-label">{{ stat.label }}</div>
                        <div class="stat-value" :style="{ color: stat.color }">
                            {{ stat.value }}
                            <span class="stat-unit">{{ stat.unit }}</span>
                        </div>
                        <div class="stat-trend" v-if="stat.trend">
                            <el-icon :style="{ fontWeight: 'bolder', color: stat.trend > 0 ? '#67c23a' : '#f56c6c' }">
                                <ArrowUp v-if="stat.trend > 0" />
                                <ArrowDown v-else />
                            </el-icon>
                            <span>{{ Math.abs(stat.trend).toFixed(1) }}%</span>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>

        <!-- 数据展示 -->
        <el-card class="data-card" shadow="never" v-if="hasData">
            <template #header>
                <div class="card-header">
                    <b>数据展示</b>
                    <div class="header-actions">
                        <el-radio-group v-model="displayMode" size="small">
                            <el-radio-button :value="'chart'">图表</el-radio-button>
                            <el-radio-button :value="'table'">表格</el-radio-button>
                        </el-radio-group>
                        <el-button size="small" @click="refreshData" :loading="queryLoading">
                            <el-icon>
                                <Refresh />
                            </el-icon>
                            刷新
                        </el-button>
                    </div>
                </div>
            </template>

            <!-- 图表展示 -->
            <div v-if="displayMode === 'chart'" class="chart-container">
                <!-- 图表工具栏 -->
                <div class="chart-toolbar">
                    <div class="chart-tools">
                        <el-button-group size="small">
                            <el-button @click="resetZoom">
                                <el-icon>
                                    <ZoomOut />
                                </el-icon>
                                重置缩放
                            </el-button>
                            <el-button @click="saveChart">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                                保存图片
                            </el-button>
                            <el-button @click="toggleFullscreen">
                                <el-icon>
                                    <FullScreen />
                                </el-icon>
                                全屏
                            </el-button>
                        </el-button-group>
                    </div>
                </div>
                <div
                    ref="chartContainer"
                    class="chart-content"
                    style="width: 100%; height: 400px; min-height: 400px"
                ></div>
            </div>

            <!-- 表格展示 -->
            <div v-if="displayMode === 'table'" class="table-container">
                <el-table :data="tableData" border stripe height="100%" v-loading="queryLoading">
                    <el-table-column prop="time" label="时间" width="180" fixed="left" />
                    <el-table-column prop="layer" label="分层" width="80" v-if="queryForm.layer === null" />
                    <el-table-column
                        v-for="dataType in queryForm.dataTypes"
                        :key="dataType"
                        :prop="dataType"
                        :label="getDataTypeLabel(dataType)"
                        :formatter="(_, __, cellValue) => formatValue(cellValue, dataType)"
                    />
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="pagination.currentPage"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[20, 50, 100, 200]"
                        :total="pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </el-card>

        <!-- 无数据提示 -->
        <el-empty v-if="!hasData && !queryLoading" description="请选择查询条件并点击查询按钮" />
    </div>
</template>

<script>
import { Search, Download, Refresh, ArrowUp, ArrowDown, ZoomOut, Picture, FullScreen } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import * as echarts from 'echarts';
import { formatTimestamp } from '@/utils/date';
import { chineseLayer } from './well/chartUtils';
import historyDataService from '@/services/historyDataService';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';
import { smartFetchDeviceInfo } from '@/utils/deviceUtils';
import { initHistoryChart, updateHistoryChart, disposeChart, resetChartZoom, saveChartAsImage } from './historyChartUtils';

export default {
    name: 'HistoryData',
    components: {
        Search,
        Download,
        Refresh,
        ArrowUp,
        ArrowDown,
        ZoomOut,
        Picture,
        FullScreen
    },
    setup() {
        // 获取设备在线状态 store
        const deviceOnlineStore = useDeviceOnlineStore();

        return {
            deviceOnlineStore
        };
    },
    props: {
        // 设备ID（从父组件传入）
        deviceId: {
            type: [String, Number],
            default: null
        },
        // 设备信息（从父组件传入）
        deviceInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            // 查询表单
            queryForm: {
                layer: null,
                timeRange: [],
                dataTypes: ['innerPressure', 'outerPressure', 'flow', 'temperature', 'valveOpening'],
                interval: '10m'
            },

            // 快捷时间选项
            timeShortcuts: [
                {
                    text: '最近1小时',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000);
                        return [start, end];
                    }
                },
                {
                    text: '最近6小时',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 6);
                        return [start, end];
                    }
                },
                {
                    text: '最近1天',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24);
                        return [start, end];
                    }
                },
                {
                    text: '最近7天',
                    value: () => {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        return [start, end];
                    }
                }
            ],

            // 表单验证规则
            queryRules: {
                timeRange: [{ required: true, message: '请选择时间范围', trigger: 'change' }],
                dataTypes: [{ required: true, message: '请至少选择一种数据类型', trigger: 'change' }]
            },

            // 当前设备信息
            currentDeviceInfo: null,

            // 查询状态
            queryLoading: false,
            exportLoading: false,

            // 数据展示
            displayMode: 'chart',
            chartData: [],
            tableData: [],
            chartInstance: null,

            // 分页
            pagination: {
                currentPage: 1,
                pageSize: 50,
                total: 0
            },

            // 图表控制
            isFullscreen: false,

            // 数据统计
            dataStats: []
        };
    },
    computed: {
        // 当前设备ID
        currentDeviceId() {
            return this.deviceId || this.$route.params.id;
        },

        // 当前设备显示文本
        currentDeviceDisplay() {
            if (this.currentDeviceInfo) {
                return `${this.currentDeviceInfo.name} (${this.currentDeviceInfo.code || this.currentDeviceInfo.id})`;
            }
            return this.currentDeviceId ? `设备 ${this.currentDeviceId}` : '未知设备';
        },

        // 分层选项
        layerOptions() {
            if (!this.currentDeviceInfo || !this.currentDeviceInfo.level) {
                return [];
            }
            return Array.from({ length: this.currentDeviceInfo.level }, (_, i) => i + 1);
        },

        // 是否有数据
        hasData() {
            return this.chartData.length > 0 || this.tableData.length > 0;
        }
    },
    async mounted() {
        // 设置默认时间范围（最近24小时）
        const now = new Date();
        const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        this.queryForm.timeRange = [
            formatTimestamp(yesterday, 'YYYY-MM-DD HH:mm:ss'),
            formatTimestamp(now, 'YYYY-MM-DD HH:mm:ss')
        ];

        // 初始化设备信息
        await this.initializeDeviceInfo();
    },
    beforeUnmount() {
        if (this.chartInstance) {
            // 移除resize监听
            window.removeEventListener('resize', this.handleChartResize);
            disposeChart(this.chartInstance);
        }
    },
    watch: {
        // 监听路由变化
        $route: {
            handler() {
                this.initializeDeviceInfo();
            },
            deep: true
        },

        // 监听设备信息变化
        deviceInfo: {
            handler() {
                this.initializeDeviceInfo();
            },
            deep: true
        },

        // 监听设备ID变化
        deviceId() {
            this.initializeDeviceInfo();
        },

        // 监听显示模式变化
        displayMode(newMode) {
            if (newMode === 'chart' && this.hasData) {
                this.$nextTick(() => {
                    this.renderChart();
                });
            }
        }
    },
    methods: {
        // 渲染图表
        renderChart() {
            try {
                if (!this.chartData || !this.chartData.length) {
                    console.warn('图表数据为空');
                    return;
                }

                const container = this.$refs.chartContainer;
                if (!container) {
                    console.error('图表容器未找到');
                    return;
                }

                // 销毁旧图表
                if (this.chartInstance) {
                    disposeChart(this.chartInstance);
                    this.chartInstance = null;
                }

                // 使用工具函数初始化图表
                this.chartInstance = initHistoryChart(container);

                if (!this.chartInstance) {
                    console.error('图表初始化失败');
                    return;
                }

                // 使用工具函数更新图表数据
                const dataTypeConfig = historyDataService.getDataTypeConfig();
                updateHistoryChart(
                    this.chartInstance,
                    this.chartData,
                    this.queryForm.dataTypes,
                    dataTypeConfig
                );

                // 添加resize监听
                window.addEventListener('resize', this.handleChartResize);
            } catch (error) {
                console.error('渲染图表失败:', error);
                ElMessage.error('图表渲染失败，请检查数据格式');
            }
        },

        // 处理图表容器尺寸变化
        handleChartResize() {
            if (this.chartInstance) {
                this.chartInstance.resize();
            }
        },

        // 中文数字转换
        chineseNumber(num) {
            return chineseLayer(num);
        },

        // 初始化设备信息
        async initializeDeviceInfo() {
            // 优先使用 props 传入的设备信息
            if (this.deviceId && this.deviceInfo) {
                this.currentDeviceInfo = this.deviceInfo;
                this.queryForm.layer = null;
                return;
            }

            // 智能获取设备信息（优先使用父组件数据）
            const deviceId = this.deviceId || this.$route.params?.id;
            if (deviceId) {
                const deviceInfo = await smartFetchDeviceInfo(this, deviceId, {
                    silent: true
                });

                if (deviceInfo) {
                    this.currentDeviceInfo = deviceInfo;
                    this.queryForm.layer = null; // 重置分层选择
                }
            }
        },

        // 查询数据
        async queryData() {
            // 检查设备ID
            if (!this.currentDeviceId) {
                ElMessage.error('请先选择设备');
                return;
            }

            // 表单验证
            const valid = await this.$refs.queryFormRef.validate().catch(() => false);
            if (!valid) return;

            // 构建查询参数
            const queryParams = {
                deviceId: this.currentDeviceId,
                ...this.queryForm
            };

            // 使用服务验证参数
            const validation = historyDataService.validateQueryParams(queryParams);
            if (!validation.valid) {
                ElMessage.error(validation.message);
                return;
            }

            this.queryLoading = true;
            try {
                // 格式化查询参数
                const params = historyDataService.formatQueryParams({
                    ...queryParams,
                    page: this.pagination.currentPage,
                    pageSize: this.pagination.pageSize
                });

                // 调用服务查询数据
                const result = await historyDataService.queryHistoryData(params);

                if (result) {
                    console.log('接收到查询结果:', result);
                    this.chartData = result.chartData || [];
                    this.tableData = result.tableData || [];
                    this.pagination.total = result.total || 0;

                    console.log('设置图表数据:', {
                        chartDataLength: this.chartData.length,
                        tableDataLength: this.tableData.length,
                        total: this.pagination.total,
                        displayMode: this.displayMode
                    });

                    // 计算数据统计
                    this.calculateDataStats();

                    // 更新图表
                    if (this.displayMode === 'chart' && this.chartData.length > 0) {
                        console.log('准备渲染图表...');
                        await this.$nextTick();

                        // 延迟渲染，确保DOM更新完成
                        setTimeout(() => {
                            this.renderChart();
                        }, 200);
                    }

                    ElMessage.success(`查询成功，共找到 ${this.pagination.total} 条数据`);
                }
            } catch (error) {
                console.error('查询历史数据失败:', error);
                ElMessage.error('查询历史数据失败');
            } finally {
                this.queryLoading = false;
            }
        },

        // 重置查询
        resetQuery() {
            this.$refs.queryFormRef.resetFields();
            this.chartData = [];
            this.tableData = [];
            this.pagination.currentPage = 1;
            this.pagination.total = 0;

            // 重新设置默认时间范围
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            this.queryForm.timeRange = [
                formatTimestamp(yesterday, 'YYYY-MM-DD HH:mm:ss'),
                formatTimestamp(now, 'YYYY-MM-DD HH:mm:ss')
            ];

            // 重置分层选择
            this.queryForm.layer = null;
        },

        // 导出数据
        async exportData() {
            if (!this.hasData) {
                ElMessage.warning('没有可导出的数据');
                return;
            }

            // 检查设备ID
            if (!this.currentDeviceId) {
                ElMessage.error('请先选择设备');
                return;
            }

            // 构建查询参数
            const queryParams = {
                deviceId: this.currentDeviceId,
                ...this.queryForm
            };

            // 验证查询参数
            const validation = historyDataService.validateQueryParams(queryParams);
            if (!validation.valid) {
                ElMessage.error(validation.message);
                return;
            }

            try {
                await ElMessageBox.confirm('确定要导出当前查询条件下的所有数据吗？', '导出确认', {
                    type: 'warning'
                });

                this.exportLoading = true;

                // 格式化导出参数
                const params = historyDataService.formatQueryParams(queryParams);

                // 调用服务导出数据
                const blob = await historyDataService.exportHistoryData(params);

                if (blob) {
                    // 生成文件名
                    const fileName = historyDataService.generateFileName({
                        deviceName: this.currentDeviceInfo?.name,
                        startTime: this.queryForm.timeRange[0],
                        endTime: this.queryForm.timeRange[1],
                        dataTypes: this.queryForm.dataTypes,
                        layer: this.queryForm.layer
                    });

                    // 下载文件
                    historyDataService.downloadFile(blob, fileName);
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('导出数据失败:', error);
                    ElMessage.error('导出数据失败');
                }
            } finally {
                this.exportLoading = false;
            }
        },

        // 获取数据类型标签
        getDataTypeLabel(dataType) {
            const config = historyDataService.getDataTypeConfig()[dataType];
            return config ? `${config.label} (${config.unit})` : dataType;
        },

        // 格式化数值
        formatValue(value, dataType) {
            if (value === null || value === undefined) return '-';

            const config = historyDataService.getDataTypeConfig()[dataType];
            const decimals = config ? config.decimals : 2;

            return Number(value).toFixed(decimals);
        },

        // 分页处理
        handleSizeChange(size) {
            this.pagination.pageSize = size;
            this.pagination.currentPage = 1;
            this.queryData();
        },

        handleCurrentChange(page) {
            this.pagination.currentPage = page;
            this.queryData();
        },

        // 刷新数据
        refreshData() {
            if (this.hasData) {
                this.queryData();
            }
        },

        // 计算数据统计
        calculateDataStats() {
            if (!this.chartData.length) {
                this.dataStats = [];
                return;
            }

            const dataTypeConfig = historyDataService.getDataTypeConfig();
            this.dataStats = this.queryForm.dataTypes
                .map(dataType => {
                    const config = dataTypeConfig[dataType];
                    if (!config) return null;

                    const values = this.chartData
                        .map(item => item[dataType])
                        .filter(v => v !== null && v !== undefined);
                    if (!values.length) return null;

                    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
                    const max = Math.max(...values);
                    const min = Math.min(...values);

                    // 计算趋势
                    const firstValue = values[0];
                    const lastValue = values[values.length - 1];
                    const trend = firstValue !== 0 ? ((lastValue - firstValue) / firstValue) * 100 : 0;

                    return {
                        type: dataType,
                        label: config.label,
                        value: avg.toFixed(config.decimals || 2),
                        unit: config.unit,
                        color: config.color,
                        max: max.toFixed(config.decimals || 2),
                        min: min.toFixed(config.decimals || 2),
                        trend: trend.toFixed(1)  // 保留一位小数
                    };
                })
                .filter(Boolean);
        },

        // 重置图表缩放
        resetZoom() {
            resetChartZoom(this.chartInstance);
        },

        // 保存图表为图片
        saveChart() {
            const fileName = `${this.currentDeviceInfo?.name || '设备'}_历史数据_${new Date().toISOString().slice(0, 10)}.png`;
            saveChartAsImage(this.chartInstance, fileName);
        },

        // 切换全屏
        toggleFullscreen() {
            const container = this.$refs.chartContainer?.parentElement;
            if (!container) return;

            if (!this.isFullscreen) {
                if (container.requestFullscreen) {
                    container.requestFullscreen();
                } else if (container.webkitRequestFullscreen) {
                    container.webkitRequestFullscreen();
                } else if (container.mozRequestFullScreen) {
                    container.mozRequestFullScreen();
                }
                this.isFullscreen = true;
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                }
                this.isFullscreen = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.history-data-container {
    padding: 20px;
    overflow: auto;
    // 确保 HistoryData 组件在 el-container 中正确显示
    width: 100% !important;
    flex: 1 !important;

    .query-card {
        margin-bottom: 20px;
        flex-shrink: 0;

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .header-actions {
                display: flex;
                align-items: center;
                gap: 10px;

                .el-button-group {
                    .el-button {
                        display: flex;
                        align-items: center;
                        gap: 4px;
                    }
                }
            }
        }

        .query-form {
            .el-form-item {
                margin-bottom: 16px;
            }

            .time-range-container {
                .time-shortcuts {
                    margin-top: 8px;
                    display: flex;
                    gap: 8px;
                    flex-wrap: wrap;
                }
            }

            :deep(.el-checkbox-group) {
                display: flex;
                flex-wrap: wrap;
                gap: 16px;
            }
        }
    }

    .stats-card {
        margin-bottom: 20px;
        flex-shrink: 0;

        .stats-row {
            display: flex;
            gap: 20px;
            width: 100%;

            .stat-item-wrapper {
                flex: 1;
            }

            .stat-item {
                text-align: center;
                padding: 20px;
                border: 1px solid #ebeef5;
                border-radius: 3px;
                background: #fafafa;

                .stat-label {
                    font-size: 14px;
                    color: #909399;
                    margin-bottom: 8px;
                }

                .stat-value {
                    font-size: 24px;
                    font-weight: bold;
                    margin-bottom: 4px;

                    .stat-unit {
                        font-size: 14px;
                        font-weight: normal;
                        margin-left: 4px;
                    }
                }

                .stat-trend {
                    font-size: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 4px;
                }
            }
        }
    }

    .data-card {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 500px; // 确保最小高度

        :deep(.el-card__body) {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .header-actions {
                display: flex;
                align-items: center;
                gap: 16px;

                :deep(.el-radio-group) {
                    display: flex;
                    align-items: center;
                }

                .el-button {
                    display: flex;
                    align-items: center;
                }
            }
        }

        .chart-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 10px 0;
            min-height: 600px; // 确保最小高度

            .chart-toolbar {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;
                padding: 12px;
                background: #f8f9fa;
                border-radius: 6px;
                flex-shrink: 0;

                .chart-tools {
                    display: flex;
                    gap: 8px;
                }

                .chart-legend-filter {
                    display: flex;
                    gap: 16px;
                }
            }

            // 图表容器自适应剩余空间
            .chart-content {
                flex: 1;
                min-height: 400px;
                width: 100%;
                height: 400px; // 明确设置高度
            }
        }

        .table-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;

            .el-table {
                flex: 1;
            }

            .pagination-container {
                margin-top: 20px;
                text-align: right;
                flex-shrink: 0;
            }
        }
    }
}
</style>
