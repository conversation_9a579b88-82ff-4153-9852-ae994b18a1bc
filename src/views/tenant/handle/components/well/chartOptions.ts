import * as echarts from 'echarts';

export const chartOptions = {
    color: ['#5470c6', '#800080', '#32935b', '#ff6565', '#036c7a', '#2b1651'],
    title: {
        text: '',
        subtext: '',
        top: '10px',
        left: '10px',
        textStyle: {
            color: '#2b1651',
            fontWeight: 'bold',
            fontSize: '14px'
        },
        subtextStyle: {
            lineHeight: 26,
            rich: {
                flowValue: {
                    color: '#32935b'
                },
                innerPressureValue: {
                    color: '#5470c6'
                },
                outerPressureValue: {
                    color: '#800080'
                },
                valveOpeningValue: {
                    color: '#46919a'
                }
            }
        }
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
            label: {
                backgroundColor: '#6a7985'
            }
        }
    },
    grid: [
        {
            top: '80px',
            right: '55px',
            bottom: '30px',
            left: '50px',
            containLabel: false
        }
    ],
    legend: {
        top: '10px',
        data: [
            {
                name: '内压(MPa)',
                textStyle: {
                    color: '#5470c6'
                }
            },
            {
                name: '外压(MPa)',
                textStyle: {
                    color: '#800080'
                }
            },
            {
                name: '流量(m³/d)',
                textStyle: {
                    color: '#32935b'
                }
            },
            {
                name: '温度(℃)',
                textStyle: {
                    color: '#ff6565'
                }
            },
            {
                name: '水嘴开度(%)',
                textStyle: {
                    color: '#036c7a'
                }
            }
        ],
        itemStyle: {
            opacity: 1
        },
        lineStyle: {
            opacity: 1
        },
        textStyle: {
            opacity: 0.65,
            fontSize: 12
        },
        tooltip: {
            show: true
        }
    },
    toolbox: {
        show: true,
        top: '5px',
        right: '10px',
        feature: {
            saveAsImage: {
                show: true,
                title: '保存为图片',
                // 自定义图标路径
                icon: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAOpJREFUOE/tlMENgzAMRe1NwihsQqfAnNqeEqZoNymblE1cuXIkE6AOUg89NBck8nn+Dt9BqFgppRsABCJqPTl6AtlPKT0V6OpdwR/oHpEr+K0zHMfxzMwdM7fDMMw5UluxiTEGRHwAwEREp6xdtBxj7BBRQjxbaAlUmGRT1n0XKLvq8mKhFljAxN1ielAEArItllBtLTBzg4jZ2QKmnIBaHYiosWNoofr+XVjXylnmCJClvRJYtG9rrWA2Wh+BG9BN2CFghsqz7/vr3u2Uf5zrsOZ6O+ywBrpyWPORo3mnQFqWyei+ABTE9AJTzcJC51MZOQAAAABJRU5ErkJggg==',
                onclick: () => {
                    alert('saveAsImage');
                }
            },
            myFullScreen: {
                show: true,
                title: '全屏',
                // 自定义图标路径
                icon: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAPpJREFUOE/llNEVgjAMRVMmgU10EmUK8usX2QI3kU10k+rDlBNKih4Pf+azhdsk7yWBdo4AnojciKh22A9mPtpzERmI6ODlwcxNAt4LwNEB4nEPiMdd4HTxTSe0sgR3gaO+/hFqYIt/FiXHGJsQQupREWphaImIRCJaZ8jMoe/72kCvzNw6opxflcz99YDIqk4CAFpV1anrukveS+9OM0aG7VTynvGvQB2nWRQdxyFXOPUa39s7FYUg6sKHsI2ZbUzAlm1mn276MDetZ5vc/EUgzKqjt1oKFpyZ/6GLpbgcNmEJnEFxXATaZH5eX6UFC6A3y6V9+FZ5z3gCjU7AFaY/LG8AAAAASUVORK5CYII=',
                onclick: () => {
                    alert('myFullScreen');
                }
            },
            myViewInfo: {
                show: true,
                title: '查看详情',
                // 自定义图标路径
                icon: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAZ9JREFUOE+VVMFtwzAMJGHvUWeSJpPEHsIE/EryMiAPEW/SdJJqkBhqTpUMWlJSJA8DgcTj8XQ8JiIax7Gp6/ronDvj/5s/y8xz3/cX1DE+xpgfImqIyBLR7U3ANty3IrJjY8z1AdQy86Hv+3fB0ulmAIIdAb3ELMqBM+ccpvhmZqub405VVcCxAHSPcWcR6TRguAT2+1IjZj5H3YJsf5OWAI0x0AUXCIVgBUahCRp8ohiMlmU5DMNgQ80xA9zQZ+4iEIo002maTsEV/jHiWQZojPnCmPGRVOFNRA4lUD3+BnCapr1zDoBeUzU6vObZPtG5iSw3gAqgE5E56JsxK7FclmUHWVJA/1I4RFFVVVdmvqTMQuMTWEUSUaJ0ZC/0fyaPmyUiqPeOKDKMGr4CLOi8TpWNrCzz7EXBxu88xi3dz2wTbfJkE2CnNVlSi/lFSF9SrVyji1+sZWbsLBySPY6RhlD4CAGxRlaQYM0CHV+bZVehi91NA8Ib/X6/WxXMHpQDG2zHGrAhnnwCq0THOZW2RYP6xC6wyeLsVYqr+vYXb1F/khaiVq4AAAAASUVORK5CYII=',
                onclick: () => {
                    alert('myViewInfo');
                }
            }
        }
    },
    /*dataZoom: [
        {
            type: "slider", // 滑块类型 值有slider和inside
            xAxisIndex: [0],
        },
    ],*/
    xAxis: [
        {
            type: 'category',
            boundaryGap: false,
            data: [], // 时间轴数据
            axisLine: {
                show: true,
                symbol: ['none', 'arrow'],
                symbolSize: [10, 10],
                lineStyle: {
                    width: 1,
                    color: '#555555'
                }
            },
            axisTick: {
                show: true,
                length: 5,
                lineStyle: {
                    color: '#555555',
                    width: 1
                }
            },
            axisLabel: {
                formatter: function (value: Date) {
                    const date = new Date(value);
                    const hours = date.getHours().toString().padStart(2, '0');
                    const minutes = date.getMinutes().toString().padStart(2, '0');
                    const seconds = date.getSeconds().toString().padStart(2, '0');
                    console.log(`${hours}:${minutes}:${seconds}`)
                    return `${hours}:${minutes}:${seconds}`;
                }
            }
        }
    ],
    yAxis: [
        {
            type: 'value',
            name: '压力（MPa）',
            nameGap: 26,
            nameRotate: 90,
            nameLocation: 'middle',
            nameTextStyle: {
                fontWeight: 500,
                align: 'center'
            },
            splitNumber: 8, // 分为8个刻度，其它轴都与这个保持对齐
            position: 'left',
            alignTicks: true,
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#5470c6'
                }
            },
            axisTick: {
                show: true,
                length: 5,
                lineStyle: {
                    color: '#5470C6',
                    width: 1
                }
            },
            minorTick: {
                show: true,
                splitNumber: 4,
                lineStyle: {
                    color: '#5470c6',
                    width: 1
                }
            },
            axisLabel: {
                inside: false,
                formatter: '{value}'
            }
        },
        {
            type: 'value',
            name: '流量（m³/d）',
            nameRotate: -90,
            nameLocation: 'start',
            nameTextStyle: {
                fontWeight: 500,
                align: 'right',
                verticalAlign: 'bottom',
                padding: [0, 8, 25, 0]
            },
            position: 'right',
            scale: false,
            alignTicks: true,
            axisLine: {
                show: true,
                symbol: ['none', 'none'],
                symbolSize: [10, 10],
                lineStyle: {
                    color: '#32935b'
                }
            },
            axisTick: {
                show: true,
                inside: true,
                length: 5,
                lineStyle: {
                    color: '#32935b',
                    width: 1
                }
            },
            minorTick: {
                show: true,
                splitNumber: 4,
                lineStyle: {
                    color: '#32935b',
                    width: 1
                }
            },
            axisLabel: {
                inside: false,
                formatter: '{value}'
            }
        },
        {
            type: 'value',
            name: '温度（℃）',
            nameRotate: -90,
            nameLocation: 'start',
            nameTextStyle: {
                fontWeight: 500,
                align: 'right',
                verticalAlign: 'bottom',
                padding: [0, 25, 10, 0]
            },
            position: 'right',
            offset: -55,
            scale: false,
            alignTicks: true,
            axisLine: {
                show: true,
                symbol: ['none', 'none'],
                symbolSize: [10, 10],
                lineStyle: {
                    color: '#ff6565'
                }
            },
            axisTick: {
                show: true,
                inside: true,
                length: 5,
                interval: 2,
                lineStyle: {
                    color: '#ff6565',
                    width: 1
                }
            },
            minorTick: {
                show: true,
                splitNumber: 4,
                lineStyle: {
                    color: '#ff6565',
                    width: 1
                }
            },
            axisLabel: {
                inside: true,
                formatter: '{value}℃'
            },
            minorSplitLine: {
                show: false,
                lineStyle: {
                    color: 'rgba(0,0,0,0.05)',
                    width: 1
                }
            },
            splitArea: {
                interval: 'auto',
                show: true,
                areaStyle: {
                    opacity: 0.5
                }
            }
        },
        {
            type: 'value',
            min: 0,
            max: 100,
            name: '水嘴开度（%）',
            nameRotate: -90,
            nameLocation: 'start',
            nameTextStyle: {
                fontWeight: 500,
                align: 'right',
                verticalAlign: 'bottom',
                padding: [0, 0, 10, 0]
            },
            offset: -110,
            position: 'right',
            alignTicks: false, // 在多个 y 轴为数值轴的时候，可以开启该配置项自动对齐刻度
            // 轴线
            axisLine: {
                show: true, // 是否显示轴线
                symbol: ['none', 'none'],
                symbolSize: [10, 10],
                lineStyle: {
                    color: '#036c7a'
                },
            },
            // 大刻度
            axisTick: {
                show: true,
                inside: true,
                /*length: 5, // 长度
                interval: 'auto', // 坐标轴刻度的显示间隔，在类目轴中有效
                lineStyle: {
                    color: '#036c7a',
                    width: 1
                },*/
                // 开启 alignWithLabel 并且设置刻度值
                alignWithLabel: false,
                // 刻度值列表
                value: [20, 40, 60, 80, 100]
            },
            // 小刻度
            minorTick: {
                show: true,
                lineStyle: {
                    color: '#ccc',
                    width: 1
                }
            },
            // 刻度数值
            axisLabel: {
                inside: true,
                formatter: '{value}%'
            },
            // 底色背景线条
            minorSplitLine: {
                show: false,
                lineStyle: {
                    color: 'rgba(0,0,0,0.05)',
                    width: 1
                }
            }
        }
    ],
    series: [
        {
            name: '内压(MPa)',
            type: 'line',
            smooth: true,
            showSymbol: false,
            data: [],
            lineStyle: {
                width: 2
            }
        },
        {
            name: '外压(MPa)',
            type: 'line',
            smooth: true,
            showSymbol: false,
            data: [],
            lineStyle: {
                width: 2
            }
        },
        {
            name: '流量(m³/d)',
            type: 'line',
            smooth: true,
            showSymbol: true,
            yAxisIndex: 1,
            data: [],
            lineStyle: {
                width: 2
            }
        },
        {
            name: '温度(℃)',
            type: 'line',
            smooth: true,
            showSymbol: false,
            yAxisIndex: 2,
            data: [],
            lineStyle: {
                width: 2
            },
            areaStyle: {
                opacity: 0.35,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0,
                        color: 'rgba(255,101,101,.5)'
                    },
                    {
                        offset: 1,
                        color: 'rgba(255,255,255,0.15)'
                    }
                ])
            }
        },
        {
            name: '水嘴开度(%)',
            type: 'line',
            smooth: true,
            showSymbol: false, // 显示数据线上的刻度点
            yAxisIndex: 3,
            data: [],
            lineStyle: {
                width: 1
            },
            areaStyle: {
                opacity: 0.35,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0,
                        color: 'rgba(69,181,195,0.25)'
                    },
                    {
                        offset: 1,
                        color: 'rgba(255,255,255,0.15)'
                    }
                ])
            }
        }
    ],
};
