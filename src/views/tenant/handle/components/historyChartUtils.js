/**
 * 历史数据图表工具函数
 */
import * as echarts from 'echarts';
import { markRaw } from 'vue';
import { chartOptions } from './well/chartOptions';

/**
 * 初始化历史数据图表
 * @param {HTMLElement} chartRefEl 图表容器元素
 * @param {Object} customOptions 自定义配置
 * @returns {Object} 图表实例
 */
export function initHistoryChart(chartRefEl, customOptions = {}) {
    if (!chartRefEl) {
        console.error('图表容器元素不存在');
        return null;
    }

    // 初始化图表对象
    const chartInstance = echarts.init(chartRefEl);
    // 防止对象被转换为响应式
    const myChart = markRaw(chartInstance);

    // 获取基础配置并深拷贝
    let chartOption = JSON.parse(JSON.stringify(chartOptions));

    // 合并自定义配置
    if (customOptions && Object.keys(customOptions).length > 0) {
        chartOption = deepMerge(chartOption, customOptions);
    }

    // 设置初始配置
    myChart.setOption(chartOption);
    
    return myChart;
}

/**
 * 更新历史数据图表
 * @param {Object} myChart 图表实例
 * @param {Array} chartData 图表数据
 * @param {Array} dataTypes 数据类型数组
 * @param {Object} dataTypeConfig 数据类型配置
 */
export function updateHistoryChart(myChart, chartData, dataTypes, dataTypeConfig) {
    if (!myChart || !chartData || !chartData.length) {
        console.warn('图表实例或数据为空');
        return;
    }

    try {
        // 获取当前配置
        const option = myChart.getOption();
        
        // 更新时间轴数据
        option.xAxis[0].data = chartData.map(item => item.time);

        // 构建系列数据
        const series = [];
        const legendData = [];

        // 按照固定顺序添加数据系列
        const dataTypeOrder = ['innerPressure', 'outerPressure', 'flow', 'temperature', 'valveOpening'];
        
        dataTypes.forEach(type => {
            if (!dataTypeOrder.includes(type)) return;

            const config = dataTypeConfig[type];
            if (!config) return;

            const yAxisIndex = getYAxisIndex(type);

            legendData.push({
                name: `${config.label}(${config.unit})`,
                textStyle: {
                    color: config.color
                }
            });

            const seriesConfig = {
                name: `${config.label}(${config.unit})`,
                type: 'line',
                smooth: true,
                showSymbol: type === 'flow', // 只有流量显示数据点
                yAxisIndex,
                data: chartData.map(item => item[type]),
                lineStyle: {
                    width: type === 'valveOpening' ? 1 : 2
                }
            };

            // 为温度和阀门开度添加渐变填充
            if (type === 'temperature') {
                seriesConfig.areaStyle = {
                    opacity: 0.35,
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgba(255,101,101,.5)'
                        },
                        {
                            offset: 1,
                            color: 'rgba(255,255,255,0.15)'
                        }
                    ])
                };
            } else if (type === 'valveOpening') {
                seriesConfig.areaStyle = {
                    opacity: 0.35,
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgba(69,181,195,0.25)'
                        },
                        {
                            offset: 1,
                            color: 'rgba(255,255,255,0.15)'
                        }
                    ])
                };
            }

            series.push(seriesConfig);
        });

        // 更新配置
        option.legend[0].data = legendData;
        option.series = series;

        // 添加dataZoom配置
        option.dataZoom = [{
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
            bottom: 10,
            height: 20
        }];

        // 调整grid底部空间
        if (option.grid && option.grid[0]) {
            option.grid[0].bottom = 60;
        }

        // 移除toolbox
        delete option.toolbox;

        // 设置更新后的配置
        myChart.setOption(option, true);
        
    } catch (error) {
        console.error('更新图表失败:', error);
    }
}

/**
 * 获取Y轴索引
 * @param {string} type 数据类型
 * @returns {number} Y轴索引
 */
function getYAxisIndex(type) {
    switch (type) {
        case 'flow':
            return 1;
        case 'temperature':
            return 2;
        case 'valveOpening':
            return 3;
        default:
            return 0;
    }
}

/**
 * 深度合并对象
 * @param {Object} target 目标对象
 * @param {Object} source 源对象
 * @returns {Object} 合并后的对象
 */
function deepMerge(target, source) {
    const result = { ...target };
    
    for (const key in source) {
        if (source.hasOwnProperty(key)) {
            if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                result[key] = deepMerge(target[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }
    }
    
    return result;
}

/**
 * 安全地销毁图表实例
 * @param {Object} myChart 图表实例
 */
export function disposeChart(myChart) {
    if (myChart && !myChart.isDisposed()) {
        myChart.dispose();
    }
}

/**
 * 重置图表缩放
 * @param {Object} myChart 图表实例
 */
export function resetChartZoom(myChart) {
    if (myChart && !myChart.isDisposed()) {
        myChart.dispatchAction({
            type: 'dataZoom',
            start: 0,
            end: 100
        });
    }
}

/**
 * 保存图表为图片
 * @param {Object} myChart 图表实例
 * @param {string} fileName 文件名
 */
export function saveChartAsImage(myChart, fileName) {
    if (myChart && !myChart.isDisposed()) {
        const url = myChart.getDataURL({
            type: 'png',
            pixelRatio: 2,
            backgroundColor: '#ffffff'
        });

        const link = document.createElement('a');
        link.download = fileName;
        link.href = url;
        link.click();
    }
}
