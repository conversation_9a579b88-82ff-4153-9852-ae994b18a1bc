/**
 * MQTT相关事件定义和注册
 */
import eventManager from '@/utils/eventManager';
import loggerFactory from '@/utils/logger';

// 创建自定义前缀的日志记录器
const logger = loggerFactory.createLogger('MQTTEvents');

// MQTT事件名称常量
export const MQTT_EVENTS = {
    CONNECTED: 'mqtt-connected',
    DISCONNECTED: 'mqtt-disconnected',
    AUTO_MESSAGE: 'mqtt-auto-message',
    MESSAGE_RECEIVED: 'mqtt-message-received',
    MESSAGE_SENT: 'mqtt-message-sent'
};

// 初始化状态标志
let initialized = false;

/**
 * 确保MQTT事件已初始化
 * @returns {boolean} 是否是首次初始化
 */
const ensureInitialized = () => {
    if (initialized) {
        return false; // 已经初始化过
    }

    initialize();
    initialized = true;
    return true; // 首次初始化
};

/**
 * 初始化MQTT相关事件
 */
const initialize = () => {
    if (initialized) {
        // 使用debug级别记录日志
        logger.debug('MQTT事件已经初始化，跳过重复初始化');
        return;
    }

    // 注册MQTT连接事件
    eventManager.registerEvent(
        MQTT_EVENTS.CONNECTED,
        'MQTT连接成功事件，当MQTT客户端成功连接到服务器时触发'
    );

    // 注册MQTT断开连接事件
    eventManager.registerEvent(
        MQTT_EVENTS.DISCONNECTED,
        'MQTT连接断开事件，当MQTT客户端与服务器断开连接时触发'
    );

    // 注册MQTT自动订阅消息事件
    eventManager.registerEvent(
        MQTT_EVENTS.AUTO_MESSAGE,
        'MQTT自动订阅消息事件，当收到自动订阅主题的消息时触发'
    );

    // 注册MQTT消息接收事件
    eventManager.registerEvent(
        MQTT_EVENTS.MESSAGE_RECEIVED,
        'MQTT消息接收事件，当收到任何MQTT消息时触发'
    );

    // 注册MQTT消息发送事件
    eventManager.registerEvent(
        MQTT_EVENTS.MESSAGE_SENT,
        'MQTT消息发送事件，当成功发送MQTT消息时触发'
    );

    // 使用debug级别记录日志
    logger.debug('MQTT事件注册完成');
};

/**
 * 添加MQTT连接成功事件监听器
 * @param {Function} handler 事件处理函数
 * @param {Object} context 上下文对象
 * @returns {Object} 监听器信息
 */
const onConnected = (handler, context) => {
    ensureInitialized();
    return eventManager.addEventListener(MQTT_EVENTS.CONNECTED, handler, context);
};

/**
 * 添加MQTT断开连接事件监听器
 * @param {Function} handler 事件处理函数
 * @param {Object} context 上下文对象
 * @returns {Object} 监听器信息
 */
const onDisconnected = (handler, context) => {
    ensureInitialized();
    return eventManager.addEventListener(MQTT_EVENTS.DISCONNECTED, handler, context);
};

/**
 * 添加MQTT自动订阅消息事件监听器
 * @param {Function} handler 事件处理函数
 * @param {Object} context 上下文对象
 * @returns {Object} 监听器信息
 */
const onAutoMessage = (handler, context) => {
    ensureInitialized();
    return eventManager.addEventListener(MQTT_EVENTS.AUTO_MESSAGE, handler, context);
};

/**
 * 添加MQTT消息接收事件监听器
 * @param {Function} handler 事件处理函数
 * @param {Object} context 上下文对象
 * @returns {Object} 监听器信息
 */
const onMessageReceived = (handler, context) => {
    ensureInitialized();
    return eventManager.addEventListener(MQTT_EVENTS.MESSAGE_RECEIVED, handler, context);
};

/**
 * 添加MQTT消息发送事件监听器
 * @param {Function} handler 事件处理函数
 * @param {Object} context 上下文对象
 * @returns {Object} 监听器信息
 */
const onMessageSent = (handler, context) => {
    ensureInitialized();
    return eventManager.addEventListener(MQTT_EVENTS.MESSAGE_SENT, handler, context);
};

// 导出MQTT事件相关函数
export default {
    initialize,
    onConnected,
    onDisconnected,
    onAutoMessage,
    onMessageReceived,
    onMessageSent,
    EVENTS: MQTT_EVENTS
};
