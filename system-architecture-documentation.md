# 油井设备监控系统架构设计文档

## 系统概述

本系统是一个基于物联网的油井设备监控平台，采用微服务架构，实现对油井设备的实时监控、历史数据分析、告警管理等功能。

## 架构设计原则

### 1. 分层架构
- **设备层**：油井设备及传感器
- **通信层**：MQTT消息传输
- **服务层**：微服务集群
- **数据层**：多种数据库存储
- **应用层**：Web和移动端应用
- **运维层**：监控、日志、部署

### 2. 微服务设计
- **服务拆分**：按业务领域拆分服务
- **服务治理**：统一网关、服务发现
- **数据隔离**：每个服务独立数据库
- **通信方式**：HTTP REST + 消息队列

### 3. 高可用设计
- **负载均衡**：多层负载均衡
- **服务冗余**：关键服务多副本
- **数据备份**：主从复制、集群部署
- **故障转移**：自动故障检测和恢复

## 核心组件详解

### 设备层 (Device Layer)
```
油井设备 → 传感器数据采集 → MQTT发布
- 内压/外压传感器
- 流量计
- 温度传感器  
- 阀门开度传感器
- 设备状态监控
```

**数据格式示例：**
```json
{
  "deviceId": "well_001",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "innerPressure": 15.6,
    "outerPressure": 12.3,
    "flow": 120.5,
    "temperature": 45.2,
    "valveOpening": 75.0
  },
  "status": "online"
}
```

### 通信层 (Communication Layer)
```
MQTT Broker (Eclipse Mosquitto)
- 主题订阅/发布
- QoS保证
- 持久化会话
- 集群部署
```

**主题设计：**
```
/devices/{deviceId}/data     # 设备数据
/devices/{deviceId}/status   # 设备状态
/devices/{deviceId}/control  # 设备控制
/alerts/{deviceId}           # 告警信息
```

### 后端服务层 (Backend Services)

#### API网关 (Spring Gateway)
- **路由管理**：统一入口，路由分发
- **认证授权**：JWT token验证
- **限流熔断**：防止服务过载
- **监控日志**：请求追踪和日志

#### 微服务集群
1. **设备管理服务 (Device Service)**
   - 设备注册、配置管理
   - 设备状态维护
   - 设备分组管理

2. **数据采集服务 (Data Collection)**
   - MQTT消息订阅
   - 数据解析和验证
   - 数据存储和转发

3. **历史数据服务 (History Service)**
   - 时序数据查询
   - 数据聚合计算
   - 报表生成

4. **实时监控服务 (Monitor Service)**
   - 实时数据推送
   - WebSocket连接管理
   - 数据缓存管理

5. **告警服务 (Alert Service)**
   - 规则引擎
   - 告警触发和通知
   - 告警历史管理

6. **用户管理服务 (User Service)**
   - 用户认证授权
   - 权限管理
   - 组织架构管理

### 数据存储层 (Data Storage)

#### 关系数据库 (MySQL/PostgreSQL)
```sql
-- 设备信息表
CREATE TABLE devices (
    id BIGINT PRIMARY KEY,
    code VARCHAR(50) UNIQUE,
    name VARCHAR(100),
    location VARCHAR(200),
    level INT,
    status ENUM('online', 'offline', 'maintenance'),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY,
    username VARCHAR(50) UNIQUE,
    password VARCHAR(255),
    email VARCHAR(100),
    role ENUM('admin', 'operator', 'viewer'),
    created_at TIMESTAMP
);
```

#### 时序数据库 (InfluxDB)
```sql
-- 设备数据表
CREATE MEASUREMENT device_data (
    time TIMESTAMP,
    device_id TAG,
    inner_pressure FIELD,
    outer_pressure FIELD,
    flow FIELD,
    temperature FIELD,
    valve_opening FIELD
);
```

#### 缓存数据库 (Redis)
```
# 实时数据缓存
device:real_time:{deviceId} → JSON数据
# 用户会话
session:{sessionId} → 用户信息
# 告警计数
alert:count:{deviceId} → 计数值
```

### 前端应用层 (Frontend Layer)

#### Vue.js Web应用
```
src/
├── components/          # 通用组件
│   ├── charts/         # 图表组件
│   └── forms/          # 表单组件
├── views/              # 页面视图
│   ├── dashboard/      # 监控面板
│   ├── devices/        # 设备管理
│   ├── history/        # 历史数据
│   └── alerts/         # 告警管理
├── stores/             # 状态管理
├── services/           # API服务
└── utils/              # 工具函数
```

#### 核心功能模块
1. **实时监控面板**
   - ECharts图表展示
   - WebSocket实时更新
   - 多设备切换

2. **历史数据分析**
   - 时间范围查询
   - 数据类型筛选
   - 图表缩放和导出

3. **设备管理**
   - 设备列表和详情
   - 设备配置管理
   - 设备状态监控

4. **告警管理**
   - 告警规则配置
   - 告警历史查询
   - 告警通知设置

## 数据流转过程

### 实时数据流
```
设备传感器 → MQTT发布 → 数据采集服务 → 消息队列 
→ [实时监控服务 + 历史数据存储] → 前端展示
```

### 告警流程
```
实时数据 → 告警规则引擎 → 触发告警 → 通知服务 
→ [短信/邮件/微信] → 用户接收
```

### 历史查询流程
```
用户请求 → API网关 → 历史数据服务 → 时序数据库 
→ 数据聚合 → 返回结果 → 前端图表展示
```

## 部署架构

### 容器化部署 (Kubernetes)
```yaml
# 微服务部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: device-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: device-service
  template:
    spec:
      containers:
      - name: device-service
        image: device-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
```

### 高可用配置
- **Web层**：Nginx负载均衡，多实例部署
- **服务层**：Kubernetes自动扩缩容，健康检查
- **数据层**：主从复制，读写分离，集群部署
- **监控层**：Prometheus + Grafana，全链路监控

## 安全设计

### 网络安全
- **HTTPS/TLS**：全链路加密传输
- **VPN接入**：设备端安全连接
- **防火墙**：网络访问控制

### 应用安全
- **JWT认证**：无状态token认证
- **RBAC权限**：基于角色的访问控制
- **API限流**：防止恶意攻击
- **数据脱敏**：敏感数据保护

### 数据安全
- **数据加密**：敏感数据加密存储
- **备份策略**：定期数据备份
- **审计日志**：操作行为记录

## 性能优化

### 前端优化
- **代码分割**：按需加载
- **CDN加速**：静态资源分发
- **缓存策略**：浏览器和HTTP缓存

### 后端优化
- **连接池**：数据库连接复用
- **缓存策略**：Redis多级缓存
- **异步处理**：消息队列解耦

### 数据库优化
- **索引优化**：查询性能提升
- **分库分表**：数据水平扩展
- **读写分离**：负载分担

## 监控告警

### 应用监控
- **Prometheus**：指标收集
- **Grafana**：可视化面板
- **AlertManager**：告警管理

### 日志监控
- **ELK Stack**：日志收集分析
- **链路追踪**：分布式追踪
- **错误监控**：异常告警

这个架构设计确保了系统的高可用性、可扩展性和安全性，能够满足油井设备监控的业务需求。
