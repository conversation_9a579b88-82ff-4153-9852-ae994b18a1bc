# 水嘴开度刻度优化修复

## 问题描述
水嘴开度的Y轴刻度会出现很长的小数，如 `11.111111111111111111`，影响图表的美观性和可读性。

## 问题原因分析

### 1. 原始配置问题
```javascript
axisLabel: {
    inside: true,
    formatter: function (value, index) {
        return value.toFixed(5);  // 固定保留5位小数
    }
}
```

### 2. 刻度分割问题
```javascript
//splitNumber: 5,  // 被注释掉，导致ECharts自动计算刻度
//scale: false,     // 被注释掉
```

当没有固定刻度数量时，ECharts会根据数据自动计算刻度，可能产生不规整的数值。

## 修复方案

### 1. 智能格式化刻度标签
```javascript
axisLabel: {
    inside: true,
    formatter: function (value, index) {
        // 智能格式化：整数不显示小数，小数最多保留2位
        if (value % 1 === 0) {
            return value + '%';  // 整数直接显示，如：20%、40%、60%
        } else {
            return value.toFixed(2) + '%';  // 小数保留2位，如：33.33%
        }
    }
}
```

### 2. 固定刻度数量
```javascript
splitNumber: 5,  // 固定分为5个刻度：0%, 20%, 40%, 60%, 80%, 100%
scale: false,    // 不使用自适应缩放
```

## 修复效果

### 修复前
- ❌ 刻度显示：`0.00000%`, `11.11111%`, `22.22222%`, `33.33333%`, `44.44444%`
- ❌ 数字过长，影响美观
- ❌ 精度过高，没有实际意义

### 修复后
- ✅ 整数刻度：`0%`, `20%`, `40%`, `60%`, `80%`, `100%`
- ✅ 小数刻度（如果需要）：`33.33%`, `66.67%`
- ✅ 简洁美观，易于阅读

## 技术细节

### 智能格式化逻辑
```javascript
if (value % 1 === 0) {
    // 检查是否为整数
    return value + '%';
} else {
    // 小数保留2位
    return value.toFixed(2) + '%';
}
```

### 刻度控制
- **splitNumber: 5**：将0-100%分为5个区间，产生6个刻度点
- **min: 0, max: 100**：固定范围，确保刻度的一致性
- **scale: false**：不使用自适应缩放，保持固定范围

## 其他Y轴的刻度处理

### 压力轴（MPa）
```javascript
axisLabel: {
    inside: false,
    formatter: '{value}'  // 使用默认格式化，ECharts会智能处理
}
```

### 流量轴（m³/d）
```javascript
axisLabel: {
    inside: false,
    formatter: '{value}'  // 使用默认格式化
}
```

### 温度轴（℃）
```javascript
axisLabel: {
    inside: true,
    formatter: '{value}℃'  // 添加单位，使用默认数值格式化
}
```

## 最佳实践

### 1. 根据数据特点选择格式化方式
- **百分比数据**：使用智能格式化，整数不显示小数
- **物理量数据**：使用ECharts默认格式化
- **温度数据**：添加单位标识

### 2. 控制刻度数量
- **固定范围的数据**：使用 `splitNumber` 控制刻度数量
- **动态范围的数据**：让ECharts自动计算，但提供合理的格式化函数

### 3. 保持一致性
- 同类型的轴使用相同的格式化规则
- 考虑用户的阅读习惯和数据精度需求

## 测试验证

### 测试场景
1. **正常数据**：0-100%范围内的数据
2. **边界数据**：接近0%或100%的数据
3. **小数数据**：包含小数的开度值
4. **整数数据**：整数开度值

### 预期结果
- ✅ 刻度显示简洁美观
- ✅ 数值精度合理（整数不显示小数，小数最多2位）
- ✅ 单位标识清晰（%符号）
- ✅ 刻度间隔均匀合理

这个修复确保了水嘴开度轴的刻度显示既美观又实用。
