<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="系统架构图" id="system-architecture">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 设备层 -->
        <mxCell id="device-layer" value="设备层 (Device Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="300" height="150" as="geometry" />
        </mxCell>
        <mxCell id="oil-well-1" value="油井设备 1&#xa;传感器/执行器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" vertex="1" parent="device-layer">
          <mxGeometry x="20" y="40" width="80" height="60" as="geometry" />
        </mxCell>
        <mxCell id="oil-well-2" value="油井设备 2&#xa;传感器/执行器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" vertex="1" parent="device-layer">
          <mxGeometry x="110" y="40" width="80" height="60" as="geometry" />
        </mxCell>
        <mxCell id="oil-well-n" value="油井设备 N&#xa;传感器/执行器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" vertex="1" parent="device-layer">
          <mxGeometry x="200" y="40" width="80" height="60" as="geometry" />
        </mxCell>
        
        <!-- 通信层 -->
        <mxCell id="comm-layer" value="通信层 (Communication Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="400" y="40" width="300" height="150" as="geometry" />
        </mxCell>
        <mxCell id="emqx-broker" value="EMQX MQTT Broker&#xa;消息代理服务器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ce93d8;strokeColor=#7b1fa2;" vertex="1" parent="comm-layer">
          <mxGeometry x="80" y="40" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="protocols" value="MQTT/WebSocket&#xa;HTTP/HTTPS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#8e24aa;" vertex="1" parent="comm-layer">
          <mxGeometry x="80" y="110" width="140" height="30" as="geometry" />
        </mxCell>
        
        <!-- 后端服务层 -->
        <mxCell id="backend-layer" value="后端服务层 (Backend Services)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#1b5e20;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="760" y="40" width="400" height="300" as="geometry" />
        </mxCell>
        <mxCell id="nginx-lb" value="Nginx&#xa;负载均衡" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a5d6a7;strokeColor=#388e3c;" vertex="1" parent="backend-layer">
          <mxGeometry x="160" y="30" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="php-swoole-1" value="PHP 8.3 + Swoole&#xa;API服务器 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" vertex="1" parent="backend-layer">
          <mxGeometry x="40" y="100" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="php-swoole-2" value="PHP 8.3 + Swoole&#xa;API服务器 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" vertex="1" parent="backend-layer">
          <mxGeometry x="150" y="100" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="php-swoole-3" value="PHP 8.3 + Swoole&#xa;WebSocket服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" vertex="1" parent="backend-layer">
          <mxGeometry x="260" y="100" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swoole-processes" value="Swoole进程管理&#xa;Master/Worker/Task" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dcedc8;strokeColor=#689f38;" vertex="1" parent="backend-layer">
          <mxGeometry x="120" y="180" width="160" height="50" as="geometry" />
        </mxCell>
        
        <!-- 数据存储层 -->
        <mxCell id="data-layer" value="数据存储层 (Data Storage)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="40" y="400" width="600" height="200" as="geometry" />
        </mxCell>
        <mxCell id="mysql-cluster" value="MySQL 8 集群&#xa;主从复制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;" vertex="1" parent="data-layer">
          <mxGeometry x="40" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="redis-cluster" value="Redis 集群&#xa;缓存/会话" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;" vertex="1" parent="data-layer">
          <mxGeometry x="180" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="elasticsearch" value="Elasticsearch&#xa;搜索/日志" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;" vertex="1" parent="data-layer">
          <mxGeometry x="320" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="file-storage" value="文件存储&#xa;NFS/对象存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;" vertex="1" parent="data-layer">
          <mxGeometry x="460" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 前端应用层 -->
        <mxCell id="frontend-layer" value="前端应用层 (Frontend Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#0d47a1;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="700" y="400" width="400" height="200" as="geometry" />
        </mxCell>
        <mxCell id="vue3-app" value="Vue 3 + Vite&#xa;前端应用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#90caf9;strokeColor=#1976d2;" vertex="1" parent="frontend-layer">
          <mxGeometry x="40" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pinia-store" value="Pinia&#xa;状态管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#2196f3;" vertex="1" parent="frontend-layer">
          <mxGeometry x="180" y="40" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="echarts" value="ECharts&#xa;图表组件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#2196f3;" vertex="1" parent="frontend-layer">
          <mxGeometry x="280" y="40" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="sass-styles" value="Sass/SCSS&#xa;样式系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#2196f3;" vertex="1" parent="frontend-layer">
          <mxGeometry x="180" y="90" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="element-plus" value="Element Plus&#xa;UI组件库" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#2196f3;" vertex="1" parent="frontend-layer">
          <mxGeometry x="280" y="90" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 监控运维层 -->
        <mxCell id="devops-layer" value="监控运维层 (DevOps Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="1200" y="40" width="300" height="300" as="geometry" />
        </mxCell>
        <mxCell id="zabbix" value="Zabbix&#xa;系统监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd9;strokeColor=#c2185b;" vertex="1" parent="devops-layer">
          <mxGeometry x="40" y="40" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="grafana" value="Grafana&#xa;监控面板" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd9;strokeColor=#c2185b;" vertex="1" parent="devops-layer">
          <mxGeometry x="140" y="40" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="elk-stack" value="ELK Stack&#xa;日志管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd9;strokeColor=#c2185b;" vertex="1" parent="devops-layer">
          <mxGeometry x="40" y="110" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="docker" value="Docker&#xa;容器化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd9;strokeColor=#c2185b;" vertex="1" parent="devops-layer">
          <mxGeometry x="140" y="110" width="80" height="50" as="geometry" />
        </mxCell>
        
        <!-- 外部服务层 -->
        <mxCell id="external-layer" value="外部服务层 (External Services)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="1200" y="400" width="300" height="200" as="geometry" />
        </mxCell>
        <mxCell id="sms-service" value="短信服务&#xa;告警通知" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c5e1a5;strokeColor=#689f38;" vertex="1" parent="external-layer">
          <mxGeometry x="40" y="40" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="email-service" value="邮件服务&#xa;SMTP推送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c5e1a5;strokeColor=#689f38;" vertex="1" parent="external-layer">
          <mxGeometry x="140" y="40" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="map-service" value="地图服务&#xa;设备定位" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c5e1a5;strokeColor=#689f38;" vertex="1" parent="external-layer">
          <mxGeometry x="40" y="110" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cloud-service" value="云服务&#xa;阿里云/腾讯云" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c5e1a5;strokeColor=#689f38;" vertex="1" parent="external-layer">
          <mxGeometry x="140" y="110" width="80" height="50" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <!-- 设备到EMQX -->
        <mxCell id="device-to-emqx-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#7b1fa2;strokeWidth=2;dashed=1;" edge="1" parent="1" source="oil-well-1" target="emqx-broker">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="device-to-emqx-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#7b1fa2;strokeWidth=2;dashed=1;" edge="1" parent="1" source="oil-well-2" target="emqx-broker">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="device-to-emqx-n" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#7b1fa2;strokeWidth=2;dashed=1;" edge="1" parent="1" source="oil-well-n" target="emqx-broker">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- EMQX到后端 -->
        <mxCell id="emqx-to-nginx" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4caf50;strokeWidth=2;" edge="1" parent="1" source="emqx-broker" target="nginx-lb">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Nginx到PHP服务 -->
        <mxCell id="nginx-to-php1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4caf50;strokeWidth=2;" edge="1" parent="1" source="nginx-lb" target="php-swoole-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nginx-to-php2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4caf50;strokeWidth=2;" edge="1" parent="1" source="nginx-lb" target="php-swoole-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nginx-to-ws" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4caf50;strokeWidth=2;" edge="1" parent="1" source="nginx-lb" target="php-swoole-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 后端到数据库 -->
        <mxCell id="php-to-mysql" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="1" source="php-swoole-1" target="mysql-cluster">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="php-to-redis" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="1" source="php-swoole-2" target="redis-cluster">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="php-to-es" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="1" source="php-swoole-3" target="elasticsearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 前端到后端 -->
        <mxCell id="vue-to-nginx" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#1976d2;strokeWidth=2;" edge="1" parent="1" source="vue3-app" target="nginx-lb">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="vue-to-ws" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#1976d2;strokeWidth=2;dashed=1;" edge="1" parent="1" source="vue3-app" target="php-swoole-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 监控连接 -->
        <mxCell id="monitor-to-backend" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#c2185b;strokeWidth=1;dashed=1;" edge="1" parent="1" source="zabbix" target="nginx-lb">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 外部服务连接 -->
        <mxCell id="backend-to-sms" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#689f38;strokeWidth=1;" edge="1" parent="1" source="swoole-processes" target="sms-service">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
