<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.1.1">
  <diagram name="油井注水设备监控系统架构图" id="system-architecture">
    <mxGraphModel dx="1426" dy="777" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2336" pageHeight="1654" background="none" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="device-layer" value="设备层 (<PERSON>ce Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontStyle=1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="62" y="329" width="400" height="180" as="geometry" />
        </mxCell>
        <mxCell id="oil-well-1" value="油井注水设备 1&#xa;注水泵/流量计/温度传感器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" parent="device-layer" vertex="1">
          <mxGeometry x="30" y="40" width="100" height="80" as="geometry" />
        </mxCell>
        <mxCell id="oil-well-2" value="油井注水设备 2&#xa;注水泵/流量计/温度传感器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" parent="device-layer" vertex="1">
          <mxGeometry x="150" y="40" width="100" height="80" as="geometry" />
        </mxCell>
        <mxCell id="oil-well-n" value="油井注水设备 N&#xa;注水泵/流量计/温度传感器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1976d2;" parent="device-layer" vertex="1">
          <mxGeometry x="270" y="40" width="100" height="80" as="geometry" />
        </mxCell>
        <mxCell id="device-data" value="设备数据：&#xa;• 注水流量&#xa;• 注水温度&#xa;• 设备状态&#xa;• 控制指令" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1565c0;" parent="device-layer" vertex="1">
          <mxGeometry x="30" y="130" width="340" height="40" as="geometry" />
        </mxCell>
        <mxCell id="client-layer" value="上位机客户端层 (Desktop Client Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontStyle=1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="66" y="82" width="400" height="180" as="geometry" />
        </mxCell>
        <mxCell id="desktop-client-1" value="上位机客户端 1&#xa;桌面软件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;" parent="client-layer" vertex="1">
          <mxGeometry x="50" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="desktop-client-2" value="上位机客户端 2&#xa;桌面软件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;" parent="client-layer" vertex="1">
          <mxGeometry x="200" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="client-functions" value="客户端功能：&#xa;• 设备控制（注水开关、流量调节）&#xa;• 数据采集（流量、温度等）&#xa;• 云端账号登录验证（HTTP）&#xa;• MQTT心跳发送（每10秒）&#xa;• 订阅控制主题，发布数据主题" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#ff8f00;" parent="client-layer" vertex="1">
          <mxGeometry x="30" y="110" width="340" height="60" as="geometry" />
        </mxCell>
        <mxCell id="comm-layer" value="通信层 (Communication Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;fontStyle=1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="770" y="239" width="400" height="180" as="geometry" />
        </mxCell>
        <mxCell id="emqx-broker" value="EMQX MQTT Broker&#xa;消息代理服务器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ce93d8;strokeColor=#7b1fa2;" parent="comm-layer" vertex="1">
          <mxGeometry x="130" y="40" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="mqtt-topics" value="MQTT主题：&#xa;• /devices/{id}/control - 设备控制&#xa;• /devices/{id}/data - 数据采集&#xa;• /devices/{id}/heartbeat - 心跳状态&#xa;• /devices/{id}/status - 设备状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#8e24aa;" parent="comm-layer" vertex="1">
          <mxGeometry x="30" y="110" width="340" height="60" as="geometry" />
        </mxCell>
        <mxCell id="network-status" value="&lt;div style=&quot;line-height: 150%;&quot;&gt;&amp;nbsp; &lt;b&gt;&lt;font&gt;网络状态：&lt;/font&gt;&lt;/b&gt;&lt;br&gt;&amp;nbsp; • 有网：正常MQTT通信&lt;br&gt;&amp;nbsp; • 弱网：延迟发送&lt;br&gt;&amp;nbsp; • 断网：本地缓存，恢复后同步&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd9;strokeColor=#c2185b;align=left;" parent="1" vertex="1">
          <mxGeometry x="518" y="283" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="frontend-layer" value="云端前端应用层 (Cloud Frontend Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#0d47a1;fontStyle=1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="1597" y="202" width="400" height="220" as="geometry" />
        </mxCell>
        <mxCell id="vue3-app" value="Vue 3 + Vite&#xa;Web管理平台" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#90caf9;strokeColor=#1976d2;" parent="frontend-layer" vertex="1">
          <mxGeometry x="40" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="pinia-store" value="Pinia&#xa;状态管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#2196f3;" parent="frontend-layer" vertex="1">
          <mxGeometry x="180" y="40" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="echarts" value="ECharts&#xa;图表组件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#2196f3;" parent="frontend-layer" vertex="1">
          <mxGeometry x="280" y="40" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="sass-styles" value="Sass/SCSS&#xa;样式系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#2196f3;" parent="frontend-layer" vertex="1">
          <mxGeometry x="180" y="90" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="element-plus" value="Element Plus&#xa;UI组件库" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#2196f3;" parent="frontend-layer" vertex="1">
          <mxGeometry x="280" y="90" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="frontend-functions" value="云端功能：&#xa;• 设备远程监控 • 历史数据分析&#xa;• 用户权限管理 • 告警规则配置&#xa;• 通过上位机间接控制设备" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0277bd;" parent="frontend-layer" vertex="1">
          <mxGeometry x="40" y="140" width="320" height="60" as="geometry" />
        </mxCell>
        <mxCell id="devops-layer" value="监控运维层 (DevOps Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;fontStyle=1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="1661" y="1306" width="300" height="300" as="geometry" />
        </mxCell>
        <mxCell id="zabbix" value="Zabbix&#xa;系统监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd9;strokeColor=#c2185b;" parent="devops-layer" vertex="1">
          <mxGeometry x="40" y="40" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="grafana" value="Grafana&#xa;监控面板" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd9;strokeColor=#c2185b;" parent="devops-layer" vertex="1">
          <mxGeometry x="140" y="40" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="elk-stack" value="ELK Stack&#xa;日志管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd9;strokeColor=#c2185b;" parent="devops-layer" vertex="1">
          <mxGeometry x="40" y="110" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="docker" value="Docker&#xa;容器化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd9;strokeColor=#c2185b;" parent="devops-layer" vertex="1">
          <mxGeometry x="140" y="110" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="external-layer" value="外部服务层 (External Services)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;fontStyle=1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="476" y="1329" width="300" height="200" as="geometry" />
        </mxCell>
        <mxCell id="sms-service" value="短信服务&#xa;告警通知" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c5e1a5;strokeColor=#689f38;" parent="external-layer" vertex="1">
          <mxGeometry x="40" y="40" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="email-service" value="邮件服务&#xa;SMTP推送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c5e1a5;strokeColor=#689f38;" parent="external-layer" vertex="1">
          <mxGeometry x="140" y="40" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="map-service" value="地图服务&#xa;设备定位" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c5e1a5;strokeColor=#689f38;" parent="external-layer" vertex="1">
          <mxGeometry x="40" y="110" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cloud-service" value="云服务&#xa;阿里云/腾讯云" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c5e1a5;strokeColor=#689f38;" parent="external-layer" vertex="1">
          <mxGeometry x="140" y="110" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="device-to-client-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff5722;strokeWidth=3;" parent="1" source="oil-well-1" target="desktop-client-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="device-to-client-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff5722;strokeWidth=3;" parent="1" source="oil-well-2" target="desktop-client-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="device-to-client-n" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff5722;strokeWidth=3;" parent="1" source="oil-well-n" target="desktop-client-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="client-login-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#2196f3;strokeWidth=2;dashed=1;" parent="1" source="desktop-client-1" target="php-swoole-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="200" y="400" as="sourcePoint" />
            <mxPoint x="520" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="client-login-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#2196f3;strokeWidth=2;dashed=1;" parent="1" source="desktop-client-2" target="php-swoole-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="320" y="400" as="sourcePoint" />
            <mxPoint x="520" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="client-to-emqx-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#7b1fa2;strokeWidth=3;" parent="1" source="desktop-client-1" target="emqx-broker" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="client-to-emqx-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#7b1fa2;strokeWidth=3;" parent="1" source="desktop-client-2" target="emqx-broker" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="emqx-to-mqtt-processor" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4caf50;strokeWidth=2;" parent="1" source="emqx-broker" target="php-swoole-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="auth-to-mysql" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="php-swoole-1" target="mysql-cluster" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mqtt-to-redis" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="php-swoole-2" target="redis-cluster" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mqtt-to-es" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="php-swoole-2" target="elasticsearch" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="vue-to-nginx" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#1976d2;strokeWidth=2;" parent="1" source="data-layer" target="nginx-lb" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="vue-to-ws" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#1976d2;strokeWidth=2;dashed=1;" parent="1" source="vue3-app" target="php-swoole-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="cloud-control-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff9800;strokeWidth=2;dashed=1;" parent="1" source="php-swoole-4" target="emqx-broker" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="data-flow-label-2" value="MQTT数据传输&#xa;(心跳/数据/控制)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#7b1fa2;" parent="1" vertex="1">
          <mxGeometry x="518" y="183" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="data-flow-label-4" value="云端控制指令&#xa;(通过MQTT)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#ff9800;" parent="1" vertex="1">
          <mxGeometry x="700" y="200" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#1976d2;strokeWidth=2;" edge="1" parent="1" source="backend-layer" target="data-layer">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="883" y="1336" as="sourcePoint" />
            <mxPoint x="1910" y="625" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="data-layer" value="云端数据存储层 (Cloud Data Storage)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontStyle=1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="1467" y="1055" width="640" height="180" as="geometry" />
        </mxCell>
        <mxCell id="mysql-cluster" value="MySQL 8 集群&#xa;用户/设备/配置数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;" parent="data-layer" vertex="1">
          <mxGeometry x="40" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="redis-cluster" value="Redis 集群&#xa;实时数据缓存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;" parent="data-layer" vertex="1">
          <mxGeometry x="180" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="elasticsearch" value="Elasticsearch&#xa;历史数据/日志" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;" parent="data-layer" vertex="1">
          <mxGeometry x="320" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="file-storage" value="文件存储&#xa;报告/图片" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcc80;strokeColor=#f57c00;" parent="data-layer" vertex="1">
          <mxGeometry x="460" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="data-flow" value="数据存储：&#xa;• 用户账号信息 • 设备注册信息 • 注水流量历史数据&#xa;• 温度历史数据 • 设备状态日志 • 告警记录" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#ff8f00;" parent="data-layer" vertex="1">
          <mxGeometry x="40" y="110" width="540" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wam68u_NnZaqKmoTfJ2d-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#1976d2;strokeWidth=2;" edge="1" parent="1" source="vue3-app" target="backend-layer">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="945" y="672" as="sourcePoint" />
            <mxPoint x="1787" y="1055" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="backend-layer" value="云端后端服务层 (Cloud Backend Services)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#1b5e20;fontStyle=1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="1371" y="669" width="640" height="220" as="geometry" />
        </mxCell>
        <mxCell id="nginx-lb" value="Nginx&#xa;负载均衡" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#a5d6a7;strokeColor=#388e3c;" parent="backend-layer" vertex="1">
          <mxGeometry x="280" y="30" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="php-swoole-1" value="PHP 8.3 + Swoole&#xa;用户认证API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" parent="backend-layer" vertex="1">
          <mxGeometry x="40" y="100" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="php-swoole-2" value="PHP 8.3 + Swoole&#xa;MQTT数据处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" parent="backend-layer" vertex="1">
          <mxGeometry x="160" y="100" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="php-swoole-3" value="PHP 8.3 + Swoole&#xa;WebSocket服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" parent="backend-layer" vertex="1">
          <mxGeometry x="280" y="100" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="php-swoole-4" value="PHP 8.3 + Swoole&#xa;设备控制API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" parent="backend-layer" vertex="1">
          <mxGeometry x="400" y="100" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="php-swoole-5" value="PHP 8.3 + Swoole&#xa;历史数据API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#4caf50;" parent="backend-layer" vertex="1">
          <mxGeometry x="520" y="100" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="swoole-processes" value="Swoole进程管理：Master/Worker/Task&#xa;• 用户登录验证 • MQTT消息处理 • 数据库操作 • 实时推送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dcedc8;strokeColor=#689f38;" parent="backend-layer" vertex="1">
          <mxGeometry x="40" y="170" width="580" height="40" as="geometry" />
        </mxCell>
        <mxCell id="data-flow-label-1" value="直连控制&#xa;(有线/无线)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#ff5722;" parent="1" vertex="1">
          <mxGeometry x="685" y="109" width="80" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
