# HistoryData 组件 ECharts 错误修复测试

## 问题描述
在点击图表的legend或选择区段时出现错误：
```
Uncaught TypeError: Cannot read properties of undefined (reading 'type')
    at Object.reset (echarts.js?v=2e2905f8:34579:35)
```

## 修复内容

### 1. 完善系列配置
- ✅ 添加唯一的系列ID
- ✅ 完善颜色配置
- ✅ 添加空数据处理配置

### 2. 数据验证
- ✅ 添加 `validateChartData()` 方法
- ✅ 验证数据结构完整性
- ✅ 确保有效数据系列存在

### 3. 安全的图表管理
- ✅ 创建 `safeDisposeChart()` 方法
- ✅ 添加图表状态检查
- ✅ 改进错误处理

### 4. 配置优化
- ✅ 使用 `notMerge: true` 确保配置完全替换
- ✅ 添加详细的错误日志

## 测试步骤

### 手动测试
1. 打开 HistoryData 组件页面
2. 查询一些历史数据，确保图表正常显示
3. 点击图表的legend项目，验证不再出现错误
4. 使用底部的数据区域选择器，验证不再出现错误
5. 切换不同的数据类型组合
6. 重置查询，验证图表正确清理

### 预期结果
- ✅ 图表正常渲染
- ✅ Legend点击功能正常，无错误
- ✅ 数据区域选择功能正常，无错误
- ✅ 图表切换和重置功能正常
- ✅ 控制台无相关错误信息

## 关键修复点

### 系列配置完整性
```javascript
const seriesConfig = {
    name: `${config.label}(${config.unit})`,
    type: 'line',
    smooth: true,
    showSymbol: type === 'flow',
    yAxisIndex,
    data: seriesData,
    lineStyle: {
        width: type === 'valveOpening' ? 1 : 2,
        color: config.color  // 添加颜色配置
    },
    itemStyle: {
        color: config.color  // 添加项目样式
    },
    id: type,  // 添加唯一标识
    connectNulls: false  // 正确处理空数据
};
```

### 数据验证
```javascript
validateChartData() {
    // 验证数据数组
    // 验证数据结构
    // 验证必要字段
    // 验证有效数据存在
}
```

### 安全清理
```javascript
safeDisposeChart() {
    // 移除事件监听
    // 检查图表状态
    // 安全销毁实例
    // 错误处理
}
```

## 注意事项
- 修复后的代码向后兼容
- 不影响现有功能
- 增强了错误处理和稳定性
- 改进了内存管理
