# 油井注水设备监控系统架构指南

## 🎯 系统概述

本系统是一个基于**上位机客户端**的油井注水设备监控平台，采用**四层架构**设计，通过上位机作为中间层实现云端对设备的间接控制和数据采集。

## 🏗️ 架构层次说明

### 第一层：设备层 (Device Layer) - 最底层
```
油井注水设备 (物理设备)
├── 注水泵控制系统
├── 流量计传感器
├── 温度传感器
├── 压力传感器
└── 设备状态监控
```

**特点：**
- 物理设备，部署在油井现场
- 通过有线/无线方式与上位机直连
- 无法直接连接互联网

### 第二层：上位机客户端层 (Desktop Client Layer)
```
桌面客户端软件 (关键中间层)
├── 设备直连控制 (注水开关、流量调节)
├── 数据实时采集 (流量、温度、压力等)
├── 云端账号验证 (HTTP协议登录)
├── MQTT通信模块 (心跳、数据、控制)
└── 本地缓存机制 (断网时数据缓存)
```

**核心功能：**
- **设备控制**：直接控制注水设备的开关、流量调节
- **数据采集**：实时采集注水流量、温度等指标
- **云端登录**：通过HTTP协议验证云端账号
- **MQTT通信**：每10秒发送心跳，订阅控制主题，发布数据主题
- **网络处理**：
  - 有网：正常MQTT通信
  - 弱网：延迟发送，数据缓存
  - 断网：本地缓存，恢复后同步

### 第三层：通信层 (Communication Layer)
```
EMQX MQTT Broker (消息中间件)
├── /devices/{id}/control - 设备控制主题
├── /devices/{id}/data - 数据采集主题  
├── /devices/{id}/heartbeat - 心跳状态主题
└── /devices/{id}/status - 设备状态主题
```

**MQTT主题设计：**
- **控制主题**：云端下发控制指令到上位机
- **数据主题**：上位机上报设备数据到云端
- **心跳主题**：上位机每10秒发送设备在线状态
- **状态主题**：设备状态变化通知

### 第四层：云端服务层 (Cloud Services Layer)
```
云端后端服务 (PHP 8.3 + Swoole)
├── 用户认证API (上位机登录验证)
├── MQTT数据处理 (订阅设备数据)
├── WebSocket服务 (实时数据推送)
├── 设备控制API (下发控制指令)
├── 历史数据API (数据查询分析)
└── 云端前端应用 (Vue 3管理平台)
```

## 🔄 数据流转过程

### 1. 设备控制流程
```
云端用户操作 → Vue3前端 → PHP API → EMQX MQTT 
→ 上位机订阅 → 解析指令 → 直连控制设备
```

### 2. 数据采集流程  
```
设备传感器 → 上位机采集 → MQTT发布 → EMQX Broker 
→ PHP处理 → 存储数据库 → WebSocket推送 → 前端展示
```

### 3. 用户登录流程
```
上位机启动 → HTTP请求 → 云端验证 → 返回认证结果 
→ 建立MQTT连接 → 开始数据通信
```

### 4. 心跳监控流程
```
上位机定时器 → 每10秒发送心跳 → EMQX接收 
→ 更新设备在线状态 → 前端实时显示
```

## 🌐 网络连接方案

### 有网环境
- **上位机 ↔ 云端**：MQTT + HTTP正常通信
- **实时性**：数据实时上报，控制指令即时下发
- **功能完整**：所有功能正常使用

### 弱网环境  
- **数据缓存**：上位机本地缓存数据
- **延迟发送**：网络恢复时批量上传
- **控制优先**：优先保证控制指令传输

### 断网环境
- **本地控制**：上位机继续控制设备
- **数据存储**：本地数据库存储历史数据
- **恢复同步**：网络恢复后自动同步数据

## 💾 数据存储策略

### MySQL 8 (关系数据库)
```sql
-- 用户账号表
users (id, username, password, role, created_at)

-- 设备注册表  
devices (id, code, name, client_id, location, status)

-- 设备历史数据表
device_history (id, device_id, flow_rate, temperature, 
                pressure, valve_opening, created_at)

-- 告警记录表
alerts (id, device_id, alert_type, message, level, created_at)
```

### Redis (缓存数据库)
```
# 实时数据缓存
device:realtime:{device_id} → JSON数据

# 设备在线状态
device:online:{device_id} → timestamp

# 用户会话
session:{session_id} → 用户信息

# 控制指令队列
control:queue:{device_id} → 指令列表
```

### Elasticsearch (搜索引擎)
```json
{
  "device_data": {
    "device_id": "well_001",
    "timestamp": "2024-01-01T12:00:00Z",
    "flow_rate": 120.5,
    "temperature": 45.2,
    "pressure": 15.6,
    "status": "running"
  }
}
```

## 🔧 技术实现要点

### 上位机客户端技术栈
- **开发语言**：C#/.NET 或 Java
- **MQTT客户端**：Eclipse Paho 或 HiveMQ
- **HTTP客户端**：HttpClient
- **本地数据库**：SQLite
- **UI框架**：WPF 或 JavaFX

### 云端技术栈
- **后端**：PHP 8.3 + Swoole
- **前端**：Vue 3 + Pinia + Element Plus + ECharts
- **消息中间件**：EMQX MQTT Broker
- **数据库**：MySQL 8 + Redis + Elasticsearch
- **Web服务器**：Nginx

### 关键技术特性
1. **Swoole协程**：高并发MQTT消息处理
2. **WebSocket长连接**：实时数据推送
3. **MQTT QoS**：保证消息可靠传输
4. **数据缓存**：Redis提升查询性能
5. **全文搜索**：Elasticsearch历史数据检索

## 🛡️ 安全设计

### 认证授权
- **上位机登录**：HTTP Basic Auth + Token
- **MQTT认证**：用户名密码 + ClientID验证
- **API访问**：JWT Token认证
- **权限控制**：基于角色的访问控制(RBAC)

### 数据安全
- **传输加密**：HTTPS + MQTT over TLS
- **数据加密**：敏感数据AES加密存储
- **访问日志**：完整的操作审计日志
- **网络隔离**：VPN或专网接入

## 📊 监控运维

### 系统监控
- **设备在线率**：实时监控设备连接状态
- **数据传输量**：MQTT消息统计
- **API性能**：响应时间和错误率
- **数据库性能**：查询性能和连接数

### 告警机制
- **设备离线**：超过30秒无心跳告警
- **数据异常**：流量、温度超阈值告警
- **系统故障**：服务异常自动告警
- **网络异常**：通信中断告警

## 🚀 部署建议

### 生产环境部署
```
负载均衡器 (Nginx)
├── PHP应用服务器集群 (3台)
├── EMQX集群 (3台)
├── MySQL主从集群 (1主2从)
├── Redis集群 (3主3从)
└── Elasticsearch集群 (3台)
```

### 上位机部署
- **安装位置**：油井现场控制室
- **硬件要求**：工控机或普通PC
- **网络要求**：4G/5G或有线网络
- **备份方案**：本地数据备份和远程备份

这个架构设计充分考虑了油井现场的实际情况，通过上位机作为中间层，既保证了设备的可控性，又实现了云端的统一管理和监控。
