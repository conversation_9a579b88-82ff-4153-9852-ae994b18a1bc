import { fileURLToPath, URL } from 'node:url';
import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import VueDevTools from 'vite-plugin-vue-devtools';

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
    const env = loadEnv(mode, process.cwd());
    //const { VITE_APP_ENV } = env;
    const vitePlugins = [];

    vitePlugins.push(vue());
    //vitePlugins.push(VueDevTools());

    return {
        base: env.VITE_BASE_URL,
        plugins: [vitePlugins],
        //控制台输出的级别 info 、warn、error、silent
        logLevel: "info",
        // 设为false 可以避免 vite 清屏而错过在终端中打印某些关键信息
        clearScreen: true,
        resolve: {
            alias: {
                // 设置路径
                '~': path.resolve(__dirname, './'),
                '@': fileURLToPath(new URL('./src', import.meta.url)),
                'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
            },
            extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
        },
        server: {
            host: '0.0.0.0',
            port: env.VITE_APP_PORT,
            https: false, // 是否启用 http 2
            cors: true, // 为开发服务器配置 CORS , 默认启用并允许任何源
            open: false, // 服务启动时自动在浏览器中打开应用
            strictPort: false, // 设为true时端口被占用则直接退出，不会尝试下一个可用端口
            force: true, // 是否强制依赖预构建
            // HMR 相关配置
            hmr: {
                // 是否在文件更改时自动刷新
                overlay: true,
                // 其他HMR选项...
            },
            // 传递给 chokidar 的文件系统监视器选项
            /*watch: {
                ignored:["!**!/node_modules/!**"]
            },*/
            watch: {
                dirs: ['src'],
                ignore: ['node_modules/**'],
                include: ['**/*.js', '**/*.ts', '**/*.vue', '**/*.scss', '**/*.css'],
                exclude: ['**/node_modules/**']
            },
            proxy: {
                'api': {
                    target: env.VITE_APP_API_BASEURL,
                    changeOrigin: true,
                    ws: true,
                    rewrite: (path) => path.replace(/^\/api/, ""),
                }
            }
        },
        build: {
            // 浏览器兼容性  "esnext"|"modules"
            target: "modules",
            // 指定输出路径
            outDir: env.VITE_BUILD_OUTDIR,
            // 生成静态资源的存放路径
            assetsDir: "assets",
            // 小于此阈值的导入或引用资源将内联为 base64 编码，以避免额外的 http 请求。设置为 0 可以完全禁用此项
            assetsInlineLimit: 4096,
            // 启用|禁用 CSS 代码拆分
            cssCodeSplit: true,
            // 构建后是否生成 source map 文件
            sourcemap: false,
            // 默认情况下，若 outDir 在 root 目录下，则 Vite 会在构建时清空该目录。
            emptyOutDir: true,
            // 设置为 false 来禁用将构建后的文件写入磁盘
            write: true,
            // 启用\禁用 brotli 压缩大小报告
            brotliSize: true,
            // chunk 大小警告的限制
            chunkSizeWarningLimit: 500,
            // 当设置为 true，构建后将会生成 manifest.json 文件
            manifest: false,
            // 设置为 false 可以禁用最小化混淆，或是用来指定使用哪种混淆器: boolean | 'terser' | 'esbuild'
            minify: "terser", //terser 构建后文件体积更小
            // 传递给 Terser 的更多 minify 选项
            terserOptions: {
                compress: {
                    drop_console: true,
                    drop_debugger: true
                }
            },
            rollupOptions: {
                // 拆包
                output: {
                    entryFileNames: 'static/js/[name]-[hash].js',
                    // 静态资源分类打包
                    chunkFileNames: 'static/js/[name]-[hash].js',
                    assetFileNames(assetInfo) {
                        // 判断后缀分别放到不用的文件夹中
                        if (assetInfo.name.endsWith('.css')) {
                            return "static/css/[name]-[hash].[ext]"
                        }

                        if (["png", "jpg", "svg", "bmp", "gif", "webp", "png"].some(ext => assetInfo.name.endsWith(ext))) {
                            return "static/images/[name]-[hash].[ext]"
                        }

                        if (["ttf", "woff", "woff2"].some(ext => assetInfo.name.endsWith(ext))) {
                            return "static/fonts/[name]-[hash].[ext]"
                        }

                        return "static/[name]-[hash].[ext]"
                    },
                    // 第三方库拆包
                    manualChunks(id) {
                        // 静态资源分拆打包
                        if (id.includes("node_modules")) {
                            return id
                                .toString()
                                .split("node_modules/")[1]
                                .split("/")[0]
                                .toString();
                        }
                    }
                    // manualChunks: {
                    //   xgplayer: ['xgplayer'],
                    //   xlsx: ['xlsx'],
                    //   tinymce: ['tinymce'],
                    //   elicons: ['@element-plus/icons-vue']
                    // }
                }
            }
        },
        css: {
            scss: {
                additionalData: `$injectedColor:orange;`
            },
            postcss: {
                plugins: [
                    {
                        postcssPlugin: 'internal:charset-removal',
                        AtRule: {
                            charset: (atRule) => {
                                if (atRule.name === 'charset') {
                                    atRule.remove();
                                }
                            }
                        }
                    }
                ]
            }
        },
        json: {
            //是否支持从 .json 文件中进行按名导入
            namedExports: true,
            //若设置为 true 导入的json会被转为 export default JSON.parse("..") 会比转译成对象字面量性能更好
            stringify: false
        },
        ssr: {
            // 列出的是要为 SSR 强制外部化的依赖
            external: [],
            // 列出的是防止被 SSR 外部化依赖项
            noExternal: []
        }
    }
})
