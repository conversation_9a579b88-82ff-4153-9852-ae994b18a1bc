# Legend 错误修复测试

## 修复的问题

### 错误信息
```
内压(MPa) series not exists. Legend data should be same with series name or data name.
外压(MPa) series not exists. Legend data should be same with series name or data name.
流量(m³/d) series not exists. Legend data should be same with series name or data name.
温度(℃) series not exists. Legend data should be same with series name or data name.
水嘴开度(%) series not exists. Legend data should be same with series name or data name.
```

### 问题原因
1. **Legend 路径错误**：使用了 `option.legend[0].data` 而不是 `option.legend.data`
2. **Legend 数据格式错误**：使用了对象格式而不是字符串数组

### 修复方案

#### 1. 修正 Legend 路径
```javascript
// 错误的写法
option.legend[0].data = legendData;

// 正确的写法
option.legend.data = legendData;
```

#### 2. 修正 Legend 数据格式
```javascript
// 错误的格式（对象数组）
legendData.push({
    name: `${config.label}(${config.unit})`,
    textStyle: {
        color: config.color
    }
});

// 正确的格式（字符串数组）
legendData.push(`${config.label}(${config.unit})`);
```

### ECharts Legend 配置说明

#### Legend 数据格式
ECharts 的 legend.data 应该是字符串数组，每个字符串对应一个系列的名称：
```javascript
legend: {
    data: ['内压(MPa)', '外压(MPa)', '流量(m³/d)', '温度(℃)', '水嘴开度(%)']
}
```

#### 系列名称匹配
系列的 name 属性必须与 legend.data 中的字符串完全匹配：
```javascript
series: [
    {
        name: '内压(MPa)',  // 必须与 legend.data 中的字符串匹配
        type: 'line',
        data: [...]
    }
]
```

#### Legend 样式配置
Legend 的样式应该在 legend 配置对象中设置，而不是在 data 数组中：
```javascript
legend: {
    data: ['内压(MPa)', '外压(MPa)'],
    textStyle: {
        color: '#333',
        fontSize: 12
    },
    itemStyle: {
        opacity: 1
    }
}
```

## 测试验证

### 1. 基本功能测试
- [ ] 图表正常渲染
- [ ] Legend 显示正确的项目
- [ ] 控制台无 "series not exists" 错误

### 2. Legend 交互测试
- [ ] 点击 legend 项目可以显示/隐藏对应系列
- [ ] Legend 项目的颜色与系列颜色匹配
- [ ] Legend 悬停效果正常

### 3. 数据匹配测试
- [ ] 每个 legend 项目都有对应的系列
- [ ] 系列名称与 legend 数据完全匹配
- [ ] 不同数据类型组合都正常显示

## 预期结果

1. **错误消除**：控制台不再出现 "series not exists" 警告
2. **Legend 正常**：Legend 项目正确显示和交互
3. **功能完整**：所有图表功能正常工作
4. **样式保持**：图表外观与预期一致

## 技术要点

### ECharts Legend 最佳实践
1. **数据格式**：使用字符串数组，不要使用对象数组
2. **名称匹配**：确保 legend.data 与 series.name 完全匹配
3. **样式分离**：样式配置在 legend 对象中，不在 data 中
4. **动态更新**：更新 legend 时同时更新对应的系列

### 调试技巧
1. **检查匹配**：确认 legend.data 和 series.name 的对应关系
2. **控制台日志**：ECharts 会在控制台输出不匹配的警告
3. **数据验证**：在设置配置前验证数据格式的正确性

这个修复应该彻底解决 Legend 相关的错误问题。
