@startuml C4_Context
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

LAYOUT_WITH_LEGEND()

title 油井设备监控系统 - C4 Model 架构图

Person(operator, "操作员", "监控油井设备状态，查看历史数据")
Person(admin, "系统管理员", "管理用户权限，配置告警规则")
Person(maintenance, "维护人员", "接收告警通知，处理设备故障")

System_Boundary(monitoring_system, "油井设备监控系统") {
    Container(web_app, "Web应用", "Vue 3 + Vite", "提供用户界面，展示实时数据和历史图表")
    Container(mobile_app, "移动端应用", "H5/小程序", "移动端设备监控和告警接收")
    
    Container(api_gateway, "API网关", "Nginx", "负载均衡，反向代理，SSL终端")
    
    Container(api_server_1, "API服务器1", "PHP 8.3 + Swoole", "处理HTTP请求，设备管理API")
    Container(api_server_2, "API服务器2", "PHP 8.3 + Swoole", "处理MQTT消息，数据采集")
    Container(websocket_server, "WebSocket服务器", "PHP 8.3 + Swoole", "实时数据推送，长连接管理")
    Container(task_processor, "异步任务处理器", "Swoole Task进程", "告警检测，数据分析，通知发送")
    
    ContainerDb(mysql_db, "MySQL数据库", "MySQL 8.0", "存储设备信息，用户数据，配置信息")
    ContainerDb(redis_cache, "Redis缓存", "Redis 7.x", "实时数据缓存，会话存储，消息队列")
    ContainerDb(elasticsearch, "Elasticsearch", "ES 8.x", "历史数据索引，全文搜索，日志存储")
    Container(file_storage, "文件存储", "NFS/对象存储", "图片，报告，日志文件存储")
}

System_Ext(mqtt_broker, "EMQX MQTT Broker", "消息代理服务器，处理设备MQTT连接")
System_Ext(oil_well_devices, "油井设备", "传感器和执行器，采集压力、流量、温度等数据")

System_Ext(sms_service, "短信服务", "发送告警短信通知")
System_Ext(email_service, "邮件服务", "发送告警邮件和报告")
System_Ext(map_service, "地图服务", "提供设备地理位置信息")
System_Ext(monitoring_tools, "监控工具", "Zabbix, Grafana, ELK Stack")

' 用户交互
Rel(operator, web_app, "使用", "HTTPS")
Rel(operator, mobile_app, "使用", "HTTPS")
Rel(admin, web_app, "管理", "HTTPS")
Rel(maintenance, mobile_app, "接收告警", "推送通知")

' 前端到后端
Rel(web_app, api_gateway, "API调用", "HTTPS")
Rel(mobile_app, api_gateway, "API调用", "HTTPS")
Rel(web_app, websocket_server, "实时连接", "WebSocket")

' API网关分发
Rel(api_gateway, api_server_1, "路由", "HTTP")
Rel(api_gateway, api_server_2, "路由", "HTTP")
Rel(api_gateway, websocket_server, "路由", "HTTP")

' 后端服务到数据库
Rel(api_server_1, mysql_db, "读写", "MySQL协议")
Rel(api_server_2, mysql_db, "写入", "MySQL协议")
Rel(api_server_2, redis_cache, "缓存", "Redis协议")
Rel(websocket_server, redis_cache, "读取", "Redis协议")
Rel(api_server_2, elasticsearch, "索引", "HTTP")
Rel(task_processor, elasticsearch, "日志", "HTTP")
Rel(api_server_1, file_storage, "存储", "NFS/HTTP")

' 设备数据流
Rel(oil_well_devices, mqtt_broker, "发布数据", "MQTT")
Rel(mqtt_broker, api_server_2, "订阅消息", "MQTT")

' 异步任务
Rel(api_server_2, task_processor, "投递任务", "进程通信")
Rel(task_processor, sms_service, "发送短信", "HTTP API")
Rel(task_processor, email_service, "发送邮件", "SMTP")

' 外部服务
Rel(web_app, map_service, "地图显示", "HTTP API")
Rel(monitoring_tools, api_gateway, "监控", "HTTP")

@enduml

@startuml C4_Container_Detail
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

LAYOUT_WITH_LEGEND()

title 油井设备监控系统 - 容器详细视图

Container_Boundary(frontend, "前端层") {
    Container(vue_app, "Vue 3应用", "Vue 3 + Vite + Pinia", "• 响应式用户界面\n• 状态管理\n• 路由管理\n• 组件化开发")
    Container(ui_components, "UI组件库", "Element Plus + ECharts", "• 通用UI组件\n• 图表可视化\n• 样式系统")
}

Container_Boundary(backend, "后端服务层") {
    Container(nginx, "Nginx", "反向代理 + 负载均衡", "• SSL终端\n• 静态资源服务\n• 请求分发")
    
    Container_Boundary(php_cluster, "PHP + Swoole集群") {
        Container(http_server, "HTTP服务器", "Swoole HTTP Server", "• RESTful API\n• 请求处理\n• 响应格式化")
        Container(mqtt_client, "MQTT客户端", "Swoole MQTT Client", "• 消息订阅\n• 数据解析\n• 实时处理")
        Container(websocket, "WebSocket服务", "Swoole WebSocket Server", "• 长连接管理\n• 实时推送\n• 心跳检测")
        Container(task_worker, "Task进程", "Swoole Task Worker", "• 异步任务\n• 告警处理\n• 数据分析")
    }
}

Container_Boundary(data, "数据存储层") {
    ContainerDb(mysql, "MySQL集群", "MySQL 8.0 主从", "• 事务支持\n• 主从复制\n• 读写分离")
    ContainerDb(redis, "Redis集群", "Redis 7.x 集群", "• 内存缓存\n• 会话存储\n• 发布订阅")
    ContainerDb(es, "Elasticsearch", "ES 8.x 集群", "• 全文搜索\n• 日志分析\n• 数据聚合")
}

Container_Boundary(external, "外部系统") {
    System_Ext(emqx, "EMQX集群", "MQTT消息代理")
    System_Ext(devices, "油井设备", "传感器设备")
    System_Ext(notifications, "通知服务", "短信/邮件")
}

' 连接关系
Rel(vue_app, nginx, "HTTPS请求")
Rel(vue_app, websocket, "WebSocket连接")
Rel(nginx, http_server, "负载均衡")
Rel(nginx, websocket, "代理")

Rel(devices, emqx, "MQTT发布")
Rel(emqx, mqtt_client, "消息订阅")

Rel(http_server, mysql, "数据操作")
Rel(mqtt_client, redis, "缓存写入")
Rel(mqtt_client, es, "数据索引")
Rel(websocket, redis, "数据读取")

Rel(mqtt_client, task_worker, "任务投递")
Rel(task_worker, notifications, "发送通知")

@enduml
