# 简单有效的修复方案

## 问题根源
用户指出了关键问题：**legend 中的 data 数据与 series 里的 name 属性是重合的**

## 解决方案
**直接把 legend 中的 data 去掉，不要重复赋值即可**

### 修复前的问题代码
```javascript
// 构建系列数据
const series = [];
const legendData = [];

dataTypes.forEach(type => {
    // 重复设置 legend 数据
    legendData.push(`${config.label}(${config.unit})`);
    
    const seriesConfig = {
        name: `${config.label}(${config.unit})`,  // 与 legend 重复
        // ...
    };
    series.push(seriesConfig);
});

// 手动设置 legend.data，导致冲突
option.legend.data = legendData;
option.series = series;
```

### 修复后的正确代码
```javascript
// 构建系列数据
const series = [];

dataTypes.forEach(type => {
    const seriesConfig = {
        name: `${config.label}(${config.unit})`,  // ECharts 会自动用这个生成 legend
        // ...
    };
    series.push(seriesConfig);
});

// 不需要手动设置 legend.data，ECharts 会自动根据 series.name 生成
option.series = series;
```

## ECharts Legend 工作原理

### 自动生成机制
ECharts 会自动根据 `series` 中每个系列的 `name` 属性生成 `legend.data`：

```javascript
// 当设置了 series
series: [
    { name: '内压(MPa)', type: 'line', data: [...] },
    { name: '外压(MPa)', type: 'line', data: [...] }
]

// ECharts 自动生成等效的 legend.data
legend: {
    data: ['内压(MPa)', '外压(MPa)']
}
```

### 冲突原因
当手动设置 `legend.data` 时，如果与 `series.name` 不完全匹配，就会出现 "series not exists" 错误。

## 修复效果

### 1. 错误消除
- ✅ 不再出现 "series not exists" 错误
- ✅ Legend 与 series 自动匹配

### 2. 代码简化
- ✅ 移除了不必要的 `legendData` 数组
- ✅ 移除了手动设置 `legend.data` 的代码
- ✅ 减少了代码复杂度

### 3. 维护性提升
- ✅ 单一数据源：只需维护 `series.name`
- ✅ 自动同步：Legend 自动与 series 保持一致
- ✅ 减少错误：避免手动同步导致的不匹配

## 最佳实践

### 1. 让 ECharts 自动处理
```javascript
// ✅ 推荐：让 ECharts 自动生成 legend
option.series = series;

// ❌ 不推荐：手动设置 legend.data
option.legend.data = legendData;
option.series = series;
```

### 2. 只在必要时自定义
```javascript
// 只有在需要自定义 legend 样式时才手动设置
legend: {
    top: '10px',
    textStyle: { fontSize: 12 },
    // 不设置 data，让 ECharts 自动生成
}
```

### 3. 确保名称一致性
```javascript
// 如果必须手动设置，确保完全匹配
const seriesName = `${config.label}(${config.unit})`;
legendData.push(seriesName);
seriesConfig.name = seriesName;
```

## 总结

这是一个非常简单但有效的修复：
1. **移除不必要的 `legendData` 数组**
2. **移除手动设置 `option.legend.data` 的代码**
3. **让 ECharts 自动根据 `series.name` 生成 legend**

这种方法不仅解决了错误，还简化了代码，提高了维护性。感谢用户的精准指导！
